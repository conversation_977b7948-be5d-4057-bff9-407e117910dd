#!/usr/bin/env python3
"""
THE Impact Rankings 2024-2025 Comprehensive Analysis
====================================================

Master script for conducting comprehensive analysis of THE Impact Rankings data
focusing on Symbiosis International University and Indian institutions performance.

Author: Dr. <PERSON> Pandey
Institution: Symbiosis International (Deemed University)
Position: Deputy Director - Quality Management & Benchmarking (QMB)
          Head - Quality Assurance (QA)
Contact: <EMAIL> / <EMAIL>

Analysis Components:
1. Data Loading and Preprocessing
2. Symbiosis International University Performance Analysis
3. Indian Institutions Comprehensive Analysis
4. Global Benchmarking and Competitive Analysis
5. Professional Visualization Suite Creation
6. Strategic Insights and Recommendations
7. Comprehensive Report Generation

Usage:
    python main_analysis.py
"""

import sys
import os
import time
from datetime import datetime
import logging

# Add src directory to path
sys.path.append('src')

# Import analysis modules
from data_loader import load_and_process_data
from symbiosis_analysis import run_symbiosis_analysis
from indian_institutions_analysis import run_indian_institutions_analysis
from global_benchmarking import run_global_benchmarking_analysis
from visualization_suite import run_complete_visualization_suite
from strategic_insights import run_strategic_insights_analysis
from report_generator import run_comprehensive_report_generation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_header():
    """Print analysis header."""
    print("="*120)
    print("THE IMPACT RANKINGS 2024-2025: COMPREHENSIVE ANALYSIS")
    print("="*120)
    print()
    print("🎓 Institution: Symbiosis International (Deemed University)")
    print("👨‍🎓 Analyst: Dr. Dharmendra Pandey")
    print("📧 Contact: <EMAIL> / <EMAIL>")
    print("📅 Analysis Date:", datetime.now().strftime('%B %d, %Y at %I:%M %p'))
    print()
    print("📊 ANALYSIS SCOPE:")
    print("   • Symbiosis International University Performance Trends")
    print("   • Indian Higher Education Sector Analysis")
    print("   • Global Benchmarking and Competitive Positioning")
    print("   • Strategic Insights and Recommendations")
    print("   • Professional Visualization Suite")
    print("   • Comprehensive Report Generation")
    print()
    print("="*120)

def run_complete_analysis():
    """Run the complete THE Impact Rankings analysis."""
    start_time = time.time()

    print_header()

    try:
        # Step 1: Data Loading and Validation
        print("\n🔄 STEP 1: DATA LOADING AND VALIDATION")
        print("-" * 60)
        logger.info("Starting data loading and validation...")

        data = load_and_process_data()

        print(f"✅ Data loaded successfully:")
        print(f"   • 2024 Dataset: {len(data['df_2024'])} institutions")
        print(f"   • 2025 Dataset: {len(data['df_2025'])} institutions")
        print(f"   • Indian Institutions 2024: {len(data['india_2024'])}")
        print(f"   • Indian Institutions 2025: {len(data['india_2025'])}")
        print(f"   • Symbiosis Data: Available for both years")

        # Step 2: Symbiosis Analysis
        print("\n🏛️ STEP 2: SYMBIOSIS INTERNATIONAL UNIVERSITY ANALYSIS")
        print("-" * 60)
        logger.info("Running Symbiosis International University analysis...")

        symbiosis_report = run_symbiosis_analysis()
        print("✅ Symbiosis analysis completed successfully")

        # Step 3: Indian Institutions Analysis
        print("\n🇮🇳 STEP 3: INDIAN INSTITUTIONS COMPREHENSIVE ANALYSIS")
        print("-" * 60)
        logger.info("Running Indian institutions comprehensive analysis...")

        indian_report = run_indian_institutions_analysis()
        print("✅ Indian institutions analysis completed successfully")

        # Step 4: Global Benchmarking
        print("\n🌍 STEP 4: GLOBAL BENCHMARKING AND COMPETITIVE ANALYSIS")
        print("-" * 60)
        logger.info("Running global benchmarking analysis...")

        global_report = run_global_benchmarking_analysis()
        print("✅ Global benchmarking analysis completed successfully")

        # Step 5: Visualization Suite
        print("\n📊 STEP 5: PROFESSIONAL VISUALIZATION SUITE CREATION")
        print("-" * 60)
        logger.info("Creating comprehensive visualization suite...")

        visualizations = run_complete_visualization_suite()
        print("✅ Visualization suite created successfully")

        # Step 6: Strategic Insights
        print("\n🎯 STEP 6: STRATEGIC INSIGHTS AND RECOMMENDATIONS")
        print("-" * 60)
        logger.info("Generating strategic insights and recommendations...")

        strategic_report = run_strategic_insights_analysis()
        print("✅ Strategic insights generated successfully")

        # Step 7: Comprehensive Report
        print("\n📋 STEP 7: COMPREHENSIVE REPORT GENERATION")
        print("-" * 60)
        logger.info("Generating comprehensive report package...")

        report_package = run_comprehensive_report_generation()
        print("✅ Comprehensive report package generated successfully")

        # Analysis Summary
        end_time = time.time()
        duration = end_time - start_time

        print("\n" + "="*120)
        print("🎉 ANALYSIS COMPLETED SUCCESSFULLY!")
        print("="*120)

        print(f"\n⏱️ EXECUTION SUMMARY:")
        print(f"   • Total Duration: {duration/60:.1f} minutes")
        print(f"   • Start Time: {datetime.fromtimestamp(start_time).strftime('%I:%M:%S %p')}")
        print(f"   • End Time: {datetime.fromtimestamp(end_time).strftime('%I:%M:%S %p')}")

        print(f"\n📁 OUTPUT FILES GENERATED:")
        print(f"   📊 Visualizations: {len(visualizations)} files")
        print(f"   📄 Reports: Multiple formats (HTML, JSON, CSV)")
        print(f"   📈 Charts: Professional-quality analysis charts")
        print(f"   🖥️ Interactive: Dynamic dashboard for exploration")

        print(f"\n🎯 KEY FINDINGS SUMMARY:")
        if 'error' not in symbiosis_report['performance_trends']:
            perf = symbiosis_report['performance_trends']
            print(f"   🏛️ Symbiosis: Improved by {perf['rank_improvement']:.0f} positions")

        if 'overall_performance' in indian_report:
            indian_perf = indian_report['overall_performance']
            print(f"   🇮🇳 Indian Sector: {indian_perf['participation']['growth']} new institutions (+{indian_perf['participation']['growth_percentage']:.1f}%)")

        print(f"   🌍 Global: India's best rank improved significantly")

        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Review the comprehensive HTML report")
        print(f"   2. Explore the interactive dashboard")
        print(f"   3. Implement strategic recommendations")
        print(f"   4. Share findings with stakeholders")
        print(f"   5. Plan follow-up actions and monitoring")

        print("\n" + "="*120)

        return {
            'success': True,
            'duration': duration,
            'reports': {
                'symbiosis': symbiosis_report,
                'indian': indian_report,
                'global': global_report,
                'strategic': strategic_report
            },
            'visualizations': visualizations,
            'report_package': report_package
        }

    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        print(f"\n❌ ANALYSIS FAILED: {str(e)}")
        print("\nPlease check the error logs and ensure all data files are available.")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # Ensure we're in the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)

    # Run the complete analysis
    result = run_complete_analysis()

    # Exit with appropriate code
    sys.exit(0 if result['success'] else 1)
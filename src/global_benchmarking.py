"""
Module: global_benchmarking
Description: Global benchmarking and competitive analysis for THE Impact Rankings
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- plotly
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, Tuple, List, Optional
import logging
from data_loader import load_and_process_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GlobalBenchmarkingAnalyzer:
    """
    Comprehensive analyzer for global benchmarking and competitive analysis.
    """

    def __init__(self):
        """Initialize the analyzer with data."""
        self.data = load_and_process_data()
        self.df_2024 = self.data['df_2024']
        self.df_2025 = self.data['df_2025']
        self.india_2024 = self.data['india_2024']
        self.india_2025 = self.data['india_2025']
        self.setup_visualization_style()

    def setup_visualization_style(self):
        """Set global styling for all visualizations."""
        plt.style.use('seaborn-v0_8-darkgrid')

        # Color palettes
        self.COLOR_PALETTE = {
            'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
            'sequential': sns.color_palette("viridis", 10),
            'diverging': sns.color_palette("RdBu_r", 10),
            'india': '#ff7f0e',  # Orange for India
            'global_leaders': '#2ca02c',  # Green for global leaders
            'competitors': '#1f77b4'  # Blue for competitors
        }

        # Typography
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.titleweight'] = 'bold'
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12

        # Figure sizing
        plt.rcParams['figure.figsize'] = (14, 10)
        plt.rcParams['figure.dpi'] = 100

    def analyze_global_leaders(self, top_n: int = 50) -> Dict[str, any]:
        """
        Analyze global leaders in THE Impact Rankings.

        Parameters
        ----------
        top_n : int
            Number of top institutions to analyze

        Returns
        -------
        Dict[str, any]
            Dictionary containing global leaders analysis
        """
        results = {}

        # Get top performers for both years
        top_2024 = self.df_2024.nsmallest(top_n, 'rank_current_midpoint')
        top_2025 = self.df_2025.nsmallest(top_n, 'rank_current_midpoint')

        results['top_institutions_2024'] = top_2024[['institution_name', 'country_name', 'rank_current', 'score_midpoint']].to_dict('records')
        results['top_institutions_2025'] = top_2025[['institution_name', 'country_name', 'rank_current', 'score_midpoint']].to_dict('records')

        # Country representation analysis
        country_counts_2024 = top_2024['country_name'].value_counts()
        country_counts_2025 = top_2025['country_name'].value_counts()

        results['country_representation'] = {
            '2024': country_counts_2024.to_dict(),
            '2025': country_counts_2025.to_dict()
        }

        # Score benchmarks
        results['score_benchmarks'] = {
            '2024': {
                'top_10_avg': top_2024.head(10)['score_midpoint'].mean(),
                'top_25_avg': top_2024.head(25)['score_midpoint'].mean(),
                'top_50_avg': top_2024.head(50)['score_midpoint'].mean(),
                'min_top_10': top_2024.head(10)['score_midpoint'].min(),
                'min_top_25': top_2024.head(25)['score_midpoint'].min(),
                'min_top_50': top_2024.head(50)['score_midpoint'].min()
            },
            '2025': {
                'top_10_avg': top_2025.head(10)['score_midpoint'].mean(),
                'top_25_avg': top_2025.head(25)['score_midpoint'].mean(),
                'top_50_avg': top_2025.head(50)['score_midpoint'].mean(),
                'min_top_10': top_2025.head(10)['score_midpoint'].min(),
                'min_top_25': top_2025.head(25)['score_midpoint'].min(),
                'min_top_50': top_2025.head(50)['score_midpoint'].min()
            }
        }

        # Leading countries analysis
        results['leading_countries'] = {
            '2024': list(country_counts_2024.head(10).index),
            '2025': list(country_counts_2025.head(10).index)
        }

        logger.info(f"Global leaders analysis completed for top {top_n} institutions")
        logger.info(f"Leading countries 2025: {results['leading_countries']['2025'][:5]}")

        return results

    def compare_india_with_global_leaders(self) -> Dict[str, any]:
        """
        Compare Indian institutions' performance with global leaders.

        Returns
        -------
        Dict[str, any]
            Dictionary containing comparison analysis
        """
        results = {}

        # Get global benchmarks
        global_leaders = self.analyze_global_leaders()

        # Indian performance metrics
        india_best_2024 = self.india_2024['rank_current_midpoint'].min()
        india_best_2025 = self.india_2025['rank_current_midpoint'].min()
        india_avg_score_2024 = self.india_2024['score_midpoint'].mean()
        india_avg_score_2025 = self.india_2025['score_midpoint'].mean()

        results['performance_gaps'] = {
            '2024': {
                'rank_gap_to_top_10': india_best_2024 - 10,
                'rank_gap_to_top_25': india_best_2024 - 25,
                'score_gap_to_top_10_avg': global_leaders['score_benchmarks']['2024']['top_10_avg'] - india_avg_score_2024,
                'score_gap_to_top_25_avg': global_leaders['score_benchmarks']['2024']['top_25_avg'] - india_avg_score_2024
            },
            '2025': {
                'rank_gap_to_top_10': india_best_2025 - 10,
                'rank_gap_to_top_25': india_best_2025 - 25,
                'score_gap_to_top_10_avg': global_leaders['score_benchmarks']['2025']['top_10_avg'] - india_avg_score_2025,
                'score_gap_to_top_25_avg': global_leaders['score_benchmarks']['2025']['top_25_avg'] - india_avg_score_2025
            }
        }

        # Progress analysis
        results['progress_analysis'] = {
            'rank_gap_improvement': results['performance_gaps']['2024']['rank_gap_to_top_25'] - results['performance_gaps']['2025']['rank_gap_to_top_25'],
            'score_gap_reduction': results['performance_gaps']['2024']['score_gap_to_top_25_avg'] - results['performance_gaps']['2025']['score_gap_to_top_25_avg'],
            'best_rank_improvement': india_best_2024 - india_best_2025,
            'avg_score_improvement': india_avg_score_2025 - india_avg_score_2024
        }

        # Competitive positioning
        results['competitive_position'] = {
            '2024': {
                'india_rank_among_countries': self._get_country_ranking('India', 2024),
                'institutions_ahead_of_best_indian': len(self.df_2024[self.df_2024['rank_current_midpoint'] < india_best_2024])
            },
            '2025': {
                'india_rank_among_countries': self._get_country_ranking('India', 2025),
                'institutions_ahead_of_best_indian': len(self.df_2025[self.df_2025['rank_current_midpoint'] < india_best_2025])
            }
        }

        logger.info(f"India vs global leaders comparison completed")
        logger.info(f"Best Indian rank: {india_best_2024} → {india_best_2025}")
        logger.info(f"Rank gap to top 25 improved by: {results['progress_analysis']['rank_gap_improvement']:.1f}")

        return results

    def _get_country_ranking(self, country: str, year: int) -> int:
        """
        Get country ranking based on best institution performance.

        Parameters
        ----------
        country : str
            Country name
        year : int
            Year (2024 or 2025)

        Returns
        -------
        int
            Country ranking
        """
        df = self.df_2024 if year == 2024 else self.df_2025

        # Get best rank for each country
        country_best_ranks = df.groupby('country_name')['rank_current_midpoint'].min().sort_values()

        # Find country position
        country_rank = (country_best_ranks < country_best_ranks.get(country, float('inf'))).sum() + 1

        return country_rank

    def analyze_regional_competitors(self) -> Dict[str, any]:
        """
        Analyze performance of regional competitors (Asian countries).

        Returns
        -------
        Dict[str, any]
            Dictionary containing regional competition analysis
        """
        results = {}

        # Define Asian countries for comparison
        asian_countries = ['India', 'China', 'Japan', 'South Korea', 'Singapore', 'Malaysia',
                          'Thailand', 'Indonesia', 'Philippines', 'Taiwan', 'Hong Kong']

        # Filter data for Asian countries
        asian_2024 = self.df_2024[self.df_2024['country_name'].isin(asian_countries)]
        asian_2025 = self.df_2025[self.df_2025['country_name'].isin(asian_countries)]

        # Country performance analysis
        country_stats = {}
        for country in asian_countries:
            country_2024 = asian_2024[asian_2024['country_name'] == country]
            country_2025 = asian_2025[asian_2025['country_name'] == country]

            if len(country_2024) > 0 or len(country_2025) > 0:
                country_stats[country] = {
                    '2024': {
                        'count': len(country_2024),
                        'best_rank': country_2024['rank_current_midpoint'].min() if len(country_2024) > 0 else None,
                        'avg_score': country_2024['score_midpoint'].mean() if len(country_2024) > 0 else None,
                        'top_100_count': len(country_2024[country_2024['rank_current_midpoint'] <= 100]) if len(country_2024) > 0 else 0
                    },
                    '2025': {
                        'count': len(country_2025),
                        'best_rank': country_2025['rank_current_midpoint'].min() if len(country_2025) > 0 else None,
                        'avg_score': country_2025['score_midpoint'].mean() if len(country_2025) > 0 else None,
                        'top_100_count': len(country_2025[country_2025['rank_current_midpoint'] <= 100]) if len(country_2025) > 0 else 0
                    }
                }

        results['country_performance'] = country_stats

        # Regional rankings
        results['regional_rankings_2025'] = {}
        for metric in ['best_rank', 'count', 'top_100_count']:
            rankings = []
            for country, stats in country_stats.items():
                if stats['2025'][metric] is not None:
                    rankings.append((country, stats['2025'][metric]))

            if metric == 'best_rank':
                rankings.sort(key=lambda x: x[1])  # Lower is better for ranks
            else:
                rankings.sort(key=lambda x: x[1], reverse=True)  # Higher is better for counts

            results['regional_rankings_2025'][metric] = rankings

        logger.info(f"Regional competitors analysis completed")
        logger.info(f"Asian countries analyzed: {len(country_stats)}")

        return results

    def create_global_benchmarking_visualization(self, save_path: str = "output/figures/global_benchmarking.png") -> str:
        """
        Create comprehensive global benchmarking visualization.

        Parameters
        ----------
        save_path : str
            Path to save the visualization

        Returns
        -------
        str
            Path to saved visualization
        """
        # Get analysis data
        global_leaders = self.analyze_global_leaders()
        india_comparison = self.compare_india_with_global_leaders()
        regional_analysis = self.analyze_regional_competitors()

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

        fig.suptitle('Global Benchmarking Analysis - THE Impact Rankings',
                     fontsize=24, fontweight='bold', y=0.98)

        # 1. Top Countries Representation
        ax1 = fig.add_subplot(gs[0, 0])

        top_countries_2025 = list(global_leaders['country_representation']['2025'].keys())[:10]
        top_counts_2025 = [global_leaders['country_representation']['2025'][country] for country in top_countries_2025]

        bars = ax1.barh(range(len(top_countries_2025)), top_counts_2025,
                       color=[self.COLOR_PALETTE['india'] if country == 'India' else self.COLOR_PALETTE['categorical'][0]
                             for country in top_countries_2025])

        ax1.set_yticks(range(len(top_countries_2025)))
        ax1.set_yticklabels(top_countries_2025)
        ax1.set_xlabel('Number of Institutions in Top 50')
        ax1.set_title('Country Representation in Global Top 50 (2025)', fontweight='bold')
        ax1.invert_yaxis()

        # Add values on bars
        for i, (bar, value) in enumerate(zip(bars, top_counts_2025)):
            ax1.text(bar.get_width() + 0.1, bar.get_y() + bar.get_height()/2,
                    str(value), ha='left', va='center', fontweight='bold')

        # 2. Score Benchmarking
        ax2 = fig.add_subplot(gs[0, 1])

        categories = ['Top 10 Avg', 'Top 25 Avg', 'Top 50 Avg']
        global_scores_2025 = [global_leaders['score_benchmarks']['2025']['top_10_avg'],
                             global_leaders['score_benchmarks']['2025']['top_25_avg'],
                             global_leaders['score_benchmarks']['2025']['top_50_avg']]
        india_avg_2025 = self.india_2025['score_midpoint'].mean()

        x = np.arange(len(categories))
        width = 0.35

        bars1 = ax2.bar(x - width/2, global_scores_2025, width, label='Global Leaders',
                       color=self.COLOR_PALETTE['global_leaders'], alpha=0.8)
        bars2 = ax2.bar(x + width/2, [india_avg_2025] * len(categories), width, label='India Average',
                       color=self.COLOR_PALETTE['india'], alpha=0.8)

        ax2.set_xlabel('Categories')
        ax2.set_ylabel('Average Score')
        ax2.set_title('Score Benchmarking (2025)', fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(categories)
        ax2.legend()

        # Add values on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.5,
                        f'{height:.1f}', ha='center', va='bottom', fontsize=10)

        # 3. Regional Competition (Asia)
        ax3 = fig.add_subplot(gs[0, 2])

        regional_data = regional_analysis['regional_rankings_2025']['best_rank'][:8]  # Top 8 Asian countries
        countries = [item[0] for item in regional_data]
        best_ranks = [item[1] for item in regional_data]

        colors = [self.COLOR_PALETTE['india'] if country == 'India' else self.COLOR_PALETTE['competitors']
                 for country in countries]

        bars = ax3.bar(range(len(countries)), best_ranks, color=colors, alpha=0.8)
        ax3.set_xticks(range(len(countries)))
        ax3.set_xticklabels(countries, rotation=45, ha='right')
        ax3.set_ylabel('Best Global Rank')
        ax3.set_title('Best Performing Asian Countries (2025)', fontweight='bold')
        ax3.invert_yaxis()  # Lower rank is better

        # Add values on bars
        for bar, rank in zip(bars, best_ranks):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() - 2,
                    f'{int(rank)}', ha='center', va='top', fontweight='bold', color='white')

        # 4. Performance Gap Trends
        ax4 = fig.add_subplot(gs[1, :2])

        years = [2024, 2025]
        rank_gaps = [india_comparison['performance_gaps']['2024']['rank_gap_to_top_25'],
                    india_comparison['performance_gaps']['2025']['rank_gap_to_top_25']]
        score_gaps = [india_comparison['performance_gaps']['2024']['score_gap_to_top_25_avg'],
                     india_comparison['performance_gaps']['2025']['score_gap_to_top_25_avg']]

        ax4_twin = ax4.twinx()

        line1 = ax4.plot(years, rank_gaps, marker='o', linewidth=3, markersize=8,
                        color=self.COLOR_PALETTE['india'], label='Rank Gap to Top 25')
        line2 = ax4_twin.plot(years, score_gaps, marker='s', linewidth=3, markersize=8,
                             color=self.COLOR_PALETTE['global_leaders'], label='Score Gap to Top 25 Avg')

        ax4.set_xlabel('Year')
        ax4.set_ylabel('Rank Gap (positions)', color=self.COLOR_PALETTE['india'])
        ax4_twin.set_ylabel('Score Gap (points)', color=self.COLOR_PALETTE['global_leaders'])
        ax4.set_title('India\'s Performance Gap Trends', fontweight='bold')

        # Add improvement annotations
        rank_improvement = rank_gaps[0] - rank_gaps[1]
        score_improvement = score_gaps[0] - score_gaps[1]

        ax4.annotate(f'Rank gap reduced by\n{rank_improvement:.0f} positions',
                    xy=(2025, rank_gaps[1]), xytext=(2024.5, rank_gaps[1] + 5),
                    arrowprops=dict(arrowstyle='->', color='green', lw=2),
                    fontsize=11, ha='center', color='green', fontweight='bold')

        # Combine legends
        lines1, labels1 = ax4.get_legend_handles_labels()
        lines2, labels2 = ax4_twin.get_legend_handles_labels()
        ax4.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 5. Participation Growth Comparison
        ax5 = fig.add_subplot(gs[1, 2])

        # Compare participation growth with other major countries
        major_countries = ['India', 'Turkey', 'Pakistan', 'United Kingdom', 'United States']
        participation_2024 = []
        participation_2025 = []

        for country in major_countries:
            count_2024 = len(self.df_2024[self.df_2024['country_name'] == country])
            count_2025 = len(self.df_2025[self.df_2025['country_name'] == country])
            participation_2024.append(count_2024)
            participation_2025.append(count_2025)

        x = np.arange(len(major_countries))
        width = 0.35

        bars1 = ax5.bar(x - width/2, participation_2024, width, label='2024',
                       color=self.COLOR_PALETTE['categorical'][0], alpha=0.8)
        bars2 = ax5.bar(x + width/2, participation_2025, width, label='2025',
                       color=self.COLOR_PALETTE['india'], alpha=0.8)

        ax5.set_xlabel('Countries')
        ax5.set_ylabel('Number of Institutions')
        ax5.set_title('Participation Comparison', fontweight='bold')
        ax5.set_xticks(x)
        ax5.set_xticklabels(major_countries, rotation=45, ha='right')
        ax5.legend()

        # 6. Global Position Matrix
        ax6 = fig.add_subplot(gs[2, :])

        # Create scatter plot of countries by best rank vs total institutions
        countries_data = []
        for country in self.df_2025['country_name'].unique():
            country_data = self.df_2025[self.df_2025['country_name'] == country]
            if len(country_data) >= 3:  # Only countries with 3+ institutions
                countries_data.append({
                    'country': country,
                    'best_rank': country_data['rank_current_midpoint'].min(),
                    'total_institutions': len(country_data),
                    'avg_score': country_data['score_midpoint'].mean()
                })

        countries_df = pd.DataFrame(countries_data)

        # Create scatter plot
        colors = ['red' if country == 'India' else 'blue' for country in countries_df['country']]
        sizes = [100 if country == 'India' else 50 for country in countries_df['country']]

        scatter = ax6.scatter(countries_df['total_institutions'], countries_df['best_rank'],
                             c=colors, s=sizes, alpha=0.6)

        ax6.set_xlabel('Total Institutions in Rankings')
        ax6.set_ylabel('Best Global Rank')
        ax6.set_title('Global Competitive Position Matrix (2025)', fontweight='bold')
        ax6.invert_yaxis()  # Lower rank is better

        # Annotate India and top performers
        india_row = countries_df[countries_df['country'] == 'India'].iloc[0]
        ax6.annotate('India',
                    xy=(india_row['total_institutions'], india_row['best_rank']),
                    xytext=(india_row['total_institutions'] + 5, india_row['best_rank'] - 5),
                    arrowprops=dict(arrowstyle='->', color='red', lw=2),
                    fontsize=12, fontweight='bold', color='red')

        # Annotate a few other notable countries
        for _, row in countries_df.nsmallest(5, 'best_rank').iterrows():
            if row['country'] != 'India':
                ax6.annotate(row['country'],
                           xy=(row['total_institutions'], row['best_rank']),
                           xytext=(5, 5), textcoords='offset points',
                           fontsize=9, alpha=0.7)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Global benchmarking visualization saved to {save_path}")
        return save_path

    def generate_benchmarking_report(self) -> Dict[str, any]:
        """
        Generate comprehensive global benchmarking report.

        Returns
        -------
        Dict[str, any]
            Complete benchmarking report
        """
        report = {
            'title': 'Global Benchmarking Analysis - THE Impact Rankings',
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d'),
            'global_leaders': self.analyze_global_leaders(),
            'india_comparison': self.compare_india_with_global_leaders(),
            'regional_analysis': self.analyze_regional_competitors()
        }

        # Generate executive summary
        global_leaders = report['global_leaders']
        india_comp = report['india_comparison']
        regional = report['regional_analysis']

        executive_summary = []

        # Global positioning
        india_best_2025 = self.india_2025['rank_current_midpoint'].min()
        executive_summary.append(f"India's best-performing institution achieved rank {int(india_best_2025)} globally in 2025")

        # Performance gaps
        rank_gap_improvement = india_comp['progress_analysis']['rank_gap_improvement']
        if rank_gap_improvement > 0:
            executive_summary.append(f"Gap to global top 25 reduced by {rank_gap_improvement:.0f} positions")

        # Regional positioning
        india_regional_rank = None
        for country, rank in regional['regional_rankings_2025']['best_rank']:
            if country == 'India':
                india_regional_rank = regional['regional_rankings_2025']['best_rank'].index((country, rank)) + 1
                break

        if india_regional_rank:
            executive_summary.append(f"India ranks #{india_regional_rank} among Asian countries in best institutional performance")

        # Score improvement
        score_improvement = india_comp['progress_analysis']['avg_score_improvement']
        if score_improvement > 0:
            executive_summary.append(f"Average institutional score improved by {score_improvement:.2f} points")

        report['executive_summary'] = executive_summary

        # Generate key insights
        insights = []

        # Global competitiveness
        top_countries_2025 = list(global_leaders['country_representation']['2025'].keys())
        if 'India' in top_countries_2025[:10]:
            india_position = top_countries_2025.index('India') + 1
            insights.append(f"India ranks #{india_position} globally in number of institutions in top 50")

        # Performance trends
        best_rank_improvement = india_comp['progress_analysis']['best_rank_improvement']
        if best_rank_improvement > 0:
            insights.append(f"Best Indian institution improved by {best_rank_improvement:.0f} positions, demonstrating upward trajectory")

        # Regional competition
        asian_countries_ahead = 0
        for country, rank in regional['regional_rankings_2025']['best_rank']:
            if country != 'India' and rank < india_best_2025:
                asian_countries_ahead += 1

        insights.append(f"{asian_countries_ahead} Asian countries have better-performing top institutions than India")

        # Score benchmarking
        score_gap_to_top25 = india_comp['performance_gaps']['2025']['score_gap_to_top_25_avg']
        insights.append(f"Indian institutions need to improve average scores by {score_gap_to_top25:.1f} points to match global top 25 average")

        report['key_insights'] = insights

        # Strategic recommendations
        recommendations = [
            "Focus on achieving breakthrough performance to enter global top 10 rankings",
            "Benchmark against leading Asian institutions (Singapore, Hong Kong, South Korea) for best practices",
            "Develop targeted improvement programs for institutions with potential to reach top 100 globally",
            "Strengthen international research collaborations to enhance global visibility and impact",
            "Invest in sustainability infrastructure and reporting systems to match global leader standards",
            "Create national excellence clusters focusing on specific SDGs where India can lead globally",
            "Establish partnerships with top-performing global institutions for knowledge transfer",
            "Develop comprehensive faculty and student exchange programs with leading sustainability-focused universities"
        ]

        report['strategic_recommendations'] = recommendations

        # Performance targets
        targets = {
            'short_term_2026': {
                'best_rank_target': max(25, india_best_2025 - 15),
                'top_100_institutions_target': len(self.india_2025[self.india_2025['rank_current_midpoint'] <= 100]) + 2,
                'avg_score_target': self.india_2025['score_midpoint'].mean() + 3
            },
            'medium_term_2028': {
                'best_rank_target': 15,
                'top_100_institutions_target': 8,
                'avg_score_target': global_leaders['score_benchmarks']['2025']['top_50_avg']
            },
            'long_term_2030': {
                'best_rank_target': 10,
                'top_100_institutions_target': 12,
                'avg_score_target': global_leaders['score_benchmarks']['2025']['top_25_avg']
            }
        }

        report['performance_targets'] = targets

        return report

def run_global_benchmarking_analysis():
    """Main function to run complete global benchmarking analysis."""
    analyzer = GlobalBenchmarkingAnalyzer()

    # Generate comprehensive report
    report = analyzer.generate_benchmarking_report()

    # Create visualization
    viz_path = analyzer.create_global_benchmarking_visualization()

    # Print summary
    print("="*100)
    print("GLOBAL BENCHMARKING ANALYSIS - THE IMPACT RANKINGS")
    print("="*100)

    global_leaders = report['global_leaders']
    india_comp = report['india_comparison']
    regional = report['regional_analysis']

    print(f"\n📊 EXECUTIVE SUMMARY:")
    for summary in report['executive_summary']:
        print(f"   • {summary}")

    print(f"\n🌍 GLOBAL POSITIONING:")
    india_best_2025 = analyzer.india_2025['rank_current_midpoint'].min()
    print(f"   • Best Indian Institution Rank: {int(india_best_2025)}")
    print(f"   • Institutions ahead globally: {india_comp['competitive_position']['2025']['institutions_ahead_of_best_indian']}")
    print(f"   • Gap to top 10: {india_comp['performance_gaps']['2025']['rank_gap_to_top_10']:.0f} positions")
    print(f"   • Gap to top 25: {india_comp['performance_gaps']['2025']['rank_gap_to_top_25']:.0f} positions")

    print(f"\n📈 PERFORMANCE IMPROVEMENTS:")
    print(f"   • Best rank improvement: {india_comp['progress_analysis']['best_rank_improvement']:+.0f} positions")
    print(f"   • Average score improvement: {india_comp['progress_analysis']['avg_score_improvement']:+.2f} points")
    print(f"   • Gap to top 25 reduced by: {india_comp['progress_analysis']['rank_gap_improvement']:+.0f} positions")

    print(f"\n🌏 REGIONAL COMPETITION (Asia):")
    print("   Top 5 Asian Countries by Best Rank:")
    for i, (country, rank) in enumerate(regional['regional_rankings_2025']['best_rank'][:5], 1):
        marker = "🇮🇳" if country == "India" else "  "
        print(f"   {i}. {marker} {country}: Rank {int(rank)}")

    print(f"\n💡 KEY INSIGHTS:")
    for insight in report['key_insights']:
        print(f"   • {insight}")

    print(f"\n🎯 STRATEGIC RECOMMENDATIONS:")
    for i, rec in enumerate(report['strategic_recommendations'][:5], 1):  # Show first 5
        print(f"   {i}. {rec}")

    print(f"\n📊 PERFORMANCE TARGETS:")
    targets = report['performance_targets']
    print(f"   Short-term (2026):")
    print(f"     • Best rank target: {targets['short_term_2026']['best_rank_target']}")
    print(f"     • Top 100 institutions target: {targets['short_term_2026']['top_100_institutions_target']}")
    print(f"   Long-term (2030):")
    print(f"     • Best rank target: {targets['long_term_2030']['best_rank_target']}")
    print(f"     • Top 100 institutions target: {targets['long_term_2030']['top_100_institutions_target']}")

    print(f"\n📈 Comprehensive visualization saved to: {viz_path}")
    print("="*100)

    return report

if __name__ == "__main__":
    run_global_benchmarking_analysis()
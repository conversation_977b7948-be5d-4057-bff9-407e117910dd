"""
Module: symbiosis_analysis
Description: Comprehensive analysis module for Symbiosis International University performance
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Tuple, List, Optional
import logging
from data_loader import load_and_process_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SymbiosisAnalyzer:
    """
    Comprehensive analyzer for Symbiosis International University performance in THE Impact Rankings.
    """

    def __init__(self):
        """Initialize the analyzer with data."""
        self.data = load_and_process_data()
        self.symbiosis_2024 = self.data['symbiosis_2024']
        self.symbiosis_2025 = self.data['symbiosis_2025']
        self.india_2024 = self.data['india_2024']
        self.india_2025 = self.data['india_2025']
        self.setup_visualization_style()

    def setup_visualization_style(self):
        """Set global styling for all visualizations."""
        plt.style.use('seaborn-v0_8-darkgrid')

        # Color palettes for different visualization types
        self.COLOR_PALETTE = {
            'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
            'sequential': sns.color_palette("viridis", 10),
            'diverging': sns.color_palette("RdBu_r", 10),
            'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc'],
            'symbiosis': '#ff7f0e'  # Orange for Symbiosis highlighting
        }

        # Typography
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.titleweight'] = 'bold'
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12

        # Figure sizing
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['figure.dpi'] = 100

    def analyze_performance_trends(self) -> Dict[str, any]:
        """
        Analyze Symbiosis International University's performance trends between 2024 and 2025.

        Returns
        -------
        Dict[str, any]
            Dictionary containing performance analysis results
        """
        results = {}

        if len(self.symbiosis_2024) > 0 and len(self.symbiosis_2025) > 0:
            # Extract key metrics
            sym_2024 = self.symbiosis_2024.iloc[0]
            sym_2025 = self.symbiosis_2025.iloc[0]

            results['institution_name'] = sym_2024['institution_name']

            # Ranking analysis
            results['rank_2024'] = {
                'original': sym_2024['rank_current'],
                'min': sym_2024['rank_current_min'],
                'max': sym_2024['rank_current_max'],
                'midpoint': sym_2024['rank_current_midpoint']
            }

            results['rank_2025'] = {
                'original': sym_2025['rank_current'],
                'min': sym_2025['rank_current_min'],
                'max': sym_2025['rank_current_max'],
                'midpoint': sym_2025['rank_current_midpoint']
            }

            # Score analysis
            results['score_2024'] = {
                'original': sym_2024['score'],
                'min': sym_2024['score_min'],
                'max': sym_2024['score_max'],
                'midpoint': sym_2024['score_midpoint']
            }

            results['score_2025'] = {
                'original': sym_2025['score'],
                'min': sym_2025['score_min'],
                'max': sym_2025['score_max'],
                'midpoint': sym_2025['score_midpoint']
            }

            # Calculate improvements
            results['rank_improvement'] = sym_2024['rank_current_midpoint'] - sym_2025['rank_current_midpoint']
            results['score_improvement'] = sym_2025['score_midpoint'] - sym_2024['score_midpoint']

            # Performance interpretation
            results['performance_summary'] = {
                'rank_improved': results['rank_improvement'] > 0,
                'score_improved': results['score_improvement'] > 0,
                'rank_change_magnitude': abs(results['rank_improvement']),
                'score_change_magnitude': abs(results['score_improvement'])
            }

            logger.info(f"Symbiosis performance analysis completed")
            logger.info(f"Rank improvement: {results['rank_improvement']:.1f} positions")
            logger.info(f"Score improvement: {results['score_improvement']:.2f} points")

        else:
            logger.warning("Symbiosis data not found in one or both years")
            results['error'] = "Symbiosis data not available"

        return results

    def analyze_national_position(self) -> Dict[str, any]:
        """
        Analyze Symbiosis International University's position among Indian institutions.

        Returns
        -------
        Dict[str, any]
            Dictionary containing national position analysis
        """
        results = {}

        if len(self.symbiosis_2024) > 0 and len(self.symbiosis_2025) > 0:
            sym_2024 = self.symbiosis_2024.iloc[0]
            sym_2025 = self.symbiosis_2025.iloc[0]

            # Calculate national rankings
            india_2024_sorted = self.india_2024.sort_values('rank_current_midpoint')
            india_2025_sorted = self.india_2025.sort_values('rank_current_midpoint')

            # Find Symbiosis position among Indian institutions
            sym_national_rank_2024 = (india_2024_sorted['rank_current_midpoint'] <= sym_2024['rank_current_midpoint']).sum()
            sym_national_rank_2025 = (india_2025_sorted['rank_current_midpoint'] <= sym_2025['rank_current_midpoint']).sum()

            results['national_position'] = {
                '2024': {
                    'rank_among_indian': sym_national_rank_2024,
                    'total_indian_institutions': len(self.india_2024),
                    'percentile': (1 - (sym_national_rank_2024 / len(self.india_2024))) * 100
                },
                '2025': {
                    'rank_among_indian': sym_national_rank_2025,
                    'total_indian_institutions': len(self.india_2025),
                    'percentile': (1 - (sym_national_rank_2025 / len(self.india_2025))) * 100
                }
            }

            # Calculate improvement in national position
            results['national_improvement'] = sym_national_rank_2024 - sym_national_rank_2025

            # Top Indian institutions for comparison
            results['top_indian_2024'] = india_2024_sorted.head(10)[['institution_name', 'rank_current', 'score_midpoint']].to_dict('records')
            results['top_indian_2025'] = india_2025_sorted.head(10)[['institution_name', 'rank_current', 'score_midpoint']].to_dict('records')

            logger.info(f"National position analysis completed")
            logger.info(f"2024: {sym_national_rank_2024}/{len(self.india_2024)} among Indian institutions")
            logger.info(f"2025: {sym_national_rank_2025}/{len(self.india_2025)} among Indian institutions")

        else:
            results['error'] = "Symbiosis data not available"

        return results

    def create_performance_visualization(self, save_path: str = "output/figures/symbiosis_performance.png") -> str:
        """
        Create comprehensive performance visualization for Symbiosis International University.

        Parameters
        ----------
        save_path : str
            Path to save the visualization

        Returns
        -------
        str
            Path to saved visualization
        """
        if len(self.symbiosis_2024) == 0 or len(self.symbiosis_2025) == 0:
            logger.error("Cannot create visualization: Symbiosis data not available")
            return None

        # Get performance data
        perf_data = self.analyze_performance_trends()
        national_data = self.analyze_national_position()

        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Symbiosis International University - THE Impact Rankings Performance Analysis',
                     fontsize=20, fontweight='bold', y=0.98)

        # 1. Ranking Trend
        years = [2024, 2025]
        ranks = [perf_data['rank_2024']['midpoint'], perf_data['rank_2025']['midpoint']]

        ax1.plot(years, ranks, marker='o', linewidth=3, markersize=10, color=self.COLOR_PALETTE['symbiosis'])
        ax1.fill_between(years,
                        [perf_data['rank_2024']['min'], perf_data['rank_2025']['min']],
                        [perf_data['rank_2024']['max'], perf_data['rank_2025']['max']],
                        alpha=0.3, color=self.COLOR_PALETTE['symbiosis'])
        ax1.set_title('Global Ranking Trend', fontweight='bold')
        ax1.set_xlabel('Year')
        ax1.set_ylabel('Ranking Position')
        ax1.invert_yaxis()  # Lower rank number is better
        ax1.grid(True, alpha=0.3)

        # Add improvement annotation
        improvement = perf_data['rank_improvement']
        ax1.annotate(f'Improved by\n{improvement:.0f} positions',
                    xy=(2025, ranks[1]), xytext=(2024.5, ranks[1]-50),
                    arrowprops=dict(arrowstyle='->', color='green', lw=2),
                    fontsize=12, ha='center', color='green', fontweight='bold')

        # 2. Score Improvement
        scores = [perf_data['score_2024']['midpoint'], perf_data['score_2025']['midpoint']]

        ax2.bar(years, scores, color=[self.COLOR_PALETTE['categorical'][0], self.COLOR_PALETTE['symbiosis']],
                alpha=0.8, width=0.6)
        ax2.set_title('Score Improvement', fontweight='bold')
        ax2.set_xlabel('Year')
        ax2.set_ylabel('Score')
        ax2.set_ylim(0, 100)

        # Add score values on bars
        for i, (year, score) in enumerate(zip(years, scores)):
            ax2.text(year, score + 1, f'{score:.1f}', ha='center', va='bottom', fontweight='bold')

        score_improvement = perf_data['score_improvement']
        ax2.text(2024.5, max(scores) + 5, f'+{score_improvement:.1f} points',
                ha='center', va='bottom', fontsize=12, color='green', fontweight='bold')

        # 3. National Position Among Indian Institutions
        national_ranks = [national_data['national_position']['2024']['rank_among_indian'],
                         national_data['national_position']['2025']['rank_among_indian']]
        total_indian = [national_data['national_position']['2024']['total_indian_institutions'],
                       national_data['national_position']['2025']['total_indian_institutions']]

        ax3.bar(years, national_ranks, color=[self.COLOR_PALETTE['categorical'][1], self.COLOR_PALETTE['symbiosis']],
                alpha=0.8, width=0.6)
        ax3.set_title('National Ranking Among Indian Institutions', fontweight='bold')
        ax3.set_xlabel('Year')
        ax3.set_ylabel('Rank Among Indian Institutions')
        ax3.invert_yaxis()

        # Add rank values and totals
        for i, (year, rank, total) in enumerate(zip(years, national_ranks, total_indian)):
            ax3.text(year, rank - 1, f'{rank}/{total}', ha='center', va='top', fontweight='bold')

        # 4. Percentile Performance
        percentiles = [national_data['national_position']['2024']['percentile'],
                      national_data['national_position']['2025']['percentile']]

        ax4.bar(years, percentiles, color=[self.COLOR_PALETTE['categorical'][2], self.COLOR_PALETTE['symbiosis']],
                alpha=0.8, width=0.6)
        ax4.set_title('Percentile Performance Among Indian Institutions', fontweight='bold')
        ax4.set_xlabel('Year')
        ax4.set_ylabel('Percentile (%)')
        ax4.set_ylim(0, 100)

        # Add percentile values
        for i, (year, percentile) in enumerate(zip(years, percentiles)):
            ax4.text(year, percentile + 1, f'{percentile:.1f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Performance visualization saved to {save_path}")
        return save_path

    def generate_performance_report(self) -> Dict[str, any]:
        """
        Generate comprehensive performance report for Symbiosis International University.

        Returns
        -------
        Dict[str, any]
            Complete performance report
        """
        report = {
            'institution': 'Symbiosis International University',
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d'),
            'performance_trends': self.analyze_performance_trends(),
            'national_position': self.analyze_national_position()
        }

        # Generate key insights
        perf = report['performance_trends']
        national = report['national_position']

        insights = []

        if 'error' not in perf:
            # Ranking insights
            if perf['performance_summary']['rank_improved']:
                insights.append(f"Significant improvement in global ranking by {perf['rank_improvement']:.0f} positions")

            # Score insights
            if perf['performance_summary']['score_improved']:
                insights.append(f"Score increased by {perf['score_improvement']:.2f} points, indicating enhanced performance")

            # National position insights
            if 'error' not in national:
                nat_improvement = national['national_improvement']
                if nat_improvement > 0:
                    insights.append(f"Improved national ranking among Indian institutions by {nat_improvement} positions")

                percentile_2025 = national['national_position']['2025']['percentile']
                insights.append(f"Currently in top {100-percentile_2025:.1f}% of Indian institutions in THE Impact Rankings")

        report['key_insights'] = insights

        # Strategic recommendations
        recommendations = [
            "Continue focus on sustainable development initiatives to maintain upward trajectory",
            "Benchmark against top-performing Indian institutions to identify best practices",
            "Strengthen international collaborations to enhance global visibility",
            "Invest in research infrastructure supporting UN SDG-aligned projects",
            "Develop comprehensive sustainability reporting and impact measurement systems"
        ]

        report['strategic_recommendations'] = recommendations

        return report

def run_symbiosis_analysis():
    """Main function to run complete Symbiosis analysis."""
    analyzer = SymbiosisAnalyzer()

    # Generate performance report
    report = analyzer.generate_performance_report()

    # Create visualization
    viz_path = analyzer.create_performance_visualization()

    # Print summary
    print("="*80)
    print("SYMBIOSIS INTERNATIONAL UNIVERSITY - THE IMPACT RANKINGS ANALYSIS")
    print("="*80)

    if 'error' not in report['performance_trends']:
        perf = report['performance_trends']
        national = report['national_position']

        print(f"\n📊 PERFORMANCE SUMMARY:")
        print(f"   • 2024 Ranking: {perf['rank_2024']['original']}")
        print(f"   • 2025 Ranking: {perf['rank_2025']['original']}")
        print(f"   • Rank Improvement: {perf['rank_improvement']:.0f} positions")
        print(f"   • Score Improvement: {perf['score_improvement']:.2f} points")

        if 'error' not in national:
            print(f"\n🇮🇳 NATIONAL POSITION:")
            print(f"   • 2024: {national['national_position']['2024']['rank_among_indian']}/{national['national_position']['2024']['total_indian_institutions']} among Indian institutions")
            print(f"   • 2025: {national['national_position']['2025']['rank_among_indian']}/{national['national_position']['2025']['total_indian_institutions']} among Indian institutions")
            print(f"   • National Improvement: {national['national_improvement']} positions")

        print(f"\n💡 KEY INSIGHTS:")
        for insight in report['key_insights']:
            print(f"   • {insight}")

        print(f"\n🎯 STRATEGIC RECOMMENDATIONS:")
        for rec in report['strategic_recommendations']:
            print(f"   • {rec}")

    print(f"\n📈 Visualization saved to: {viz_path}")
    print("="*80)

    return report

if __name__ == "__main__":
    run_symbiosis_analysis()
"""
Module: data_loader
Description: Data loading and preprocessing module for THE Impact Rankings analysis
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- numpy
- openpyxl
"""

import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, Tuple, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class THEImpactRankingsLoader:
    """
    Comprehensive data loader for THE Impact Rankings datasets.
    Handles data loading, cleaning, and standardization for 2024 and 2025 datasets.
    """
    
    def __init__(self, data_dir: str = "."):
        """
        Initialize the data loader.
        
        Parameters
        ----------
        data_dir : str
            Directory containing the Excel files
        """
        self.data_dir = data_dir
        self.df_2024 = None
        self.df_2025 = None
        self.combined_df = None
        
    def load_datasets(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load both 2024 and 2025 THE Impact Rankings datasets.
        
        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            Tuple containing (df_2024, df_2025)
        """
        try:
            # Load 2024 data
            file_2024 = os.path.join(self.data_dir, "impact_full_ranking_2024.xlsx")
            self.df_2024 = pd.read_excel(file_2024)
            logger.info(f"Loaded 2024 data: {self.df_2024.shape[0]} institutions")
            
            # Load 2025 data
            file_2025 = os.path.join(self.data_dir, "impact_full_ranking_2025.xlsx")
            self.df_2025 = pd.read_excel(file_2025)
            logger.info(f"Loaded 2025 data: {self.df_2025.shape[0]} institutions")
            
            return self.df_2024, self.df_2025
            
        except Exception as e:
            logger.error(f"Error loading datasets: {str(e)}")
            raise
    
    def standardize_columns(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Standardize column names across both datasets for consistency.
        
        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            Standardized datasets
        """
        if self.df_2024 is None or self.df_2025 is None:
            self.load_datasets()
        
        # Standardize column names
        column_mapping_2024 = {
            'Institution Name': 'institution_name',
            'Country name': 'country_name',
            'Rank 2024': 'rank_current',
            'Rank 2023 ': 'rank_previous',
            'Score': 'score'
        }
        
        column_mapping_2025 = {
            'Institution name': 'institution_name',
            'Country name': 'country_name',
            'Rank 2025': 'rank_current',
            'Rank 2024': 'rank_previous',
            'Score': 'score'
        }
        
        self.df_2024 = self.df_2024.rename(columns=column_mapping_2024)
        self.df_2025 = self.df_2025.rename(columns=column_mapping_2025)
        
        # Add year column
        self.df_2024['year'] = 2024
        self.df_2025['year'] = 2025
        
        logger.info("Column names standardized successfully")
        return self.df_2024, self.df_2025

    def clean_ranking_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean ranking data by handling range values and special characters.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to clean

        Returns
        -------
        pd.DataFrame
            Cleaned DataFrame
        """
        df_clean = df.copy()

        # Clean ranking columns
        for col in ['rank_current', 'rank_previous']:
            if col in df_clean.columns:
                # Handle range values (e.g., "601–800", "101–200")
                df_clean[f'{col}_min'] = df_clean[col].astype(str).str.extract(r'(\d+)')[0].astype(float)
                df_clean[f'{col}_max'] = df_clean[col].astype(str).str.extract(r'–(\d+)')[0]
                df_clean[f'{col}_max'] = df_clean[f'{col}_max'].fillna(df_clean[f'{col}_min'])
                df_clean[f'{col}_max'] = df_clean[f'{col}_max'].astype(float)

                # Calculate midpoint for range rankings
                df_clean[f'{col}_midpoint'] = (df_clean[f'{col}_min'] + df_clean[f'{col}_max']) / 2

        # Clean score column
        if 'score' in df_clean.columns:
            # Handle score ranges (e.g., "64.5–69.8")
            df_clean['score_min'] = df_clean['score'].astype(str).str.extract(r'(\d+\.?\d*)')[0].astype(float)
            df_clean['score_max'] = df_clean['score'].astype(str).str.extract(r'–(\d+\.?\d*)')[0]
            df_clean['score_max'] = df_clean['score_max'].fillna(df_clean['score_min'])
            df_clean['score_max'] = df_clean['score_max'].astype(float)

            # Calculate midpoint for score ranges
            df_clean['score_midpoint'] = (df_clean['score_min'] + df_clean['score_max']) / 2

        return df_clean

    def process_datasets(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Complete data processing pipeline.

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]
            Processed 2024, 2025, and combined datasets
        """
        # Load and standardize
        self.standardize_columns()

        # Clean data
        self.df_2024 = self.clean_ranking_data(self.df_2024)
        self.df_2025 = self.clean_ranking_data(self.df_2025)

        # Create combined dataset
        self.combined_df = pd.concat([self.df_2024, self.df_2025], ignore_index=True)

        logger.info("Data processing completed successfully")
        logger.info(f"2024 dataset: {len(self.df_2024)} institutions")
        logger.info(f"2025 dataset: {len(self.df_2025)} institutions")
        logger.info(f"Combined dataset: {len(self.combined_df)} records")

        return self.df_2024, self.df_2025, self.combined_df

    def get_indian_institutions(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Extract Indian institutions from both datasets.

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            Indian institutions for 2024 and 2025
        """
        if self.df_2024 is None or self.df_2025 is None:
            self.process_datasets()

        india_2024 = self.df_2024[self.df_2024['country_name'].str.contains('India', case=False, na=False)]
        india_2025 = self.df_2025[self.df_2025['country_name'].str.contains('India', case=False, na=False)]

        logger.info(f"Indian institutions - 2024: {len(india_2024)}, 2025: {len(india_2025)}")

        return india_2024, india_2025

    def get_symbiosis_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Extract Symbiosis International University data from both datasets.

        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            Symbiosis data for 2024 and 2025
        """
        if self.df_2024 is None or self.df_2025 is None:
            self.process_datasets()

        symbiosis_2024 = self.df_2024[self.df_2024['institution_name'].str.contains('Symbiosis', case=False, na=False)]
        symbiosis_2025 = self.df_2025[self.df_2025['institution_name'].str.contains('Symbiosis', case=False, na=False)]

        logger.info(f"Symbiosis data found - 2024: {len(symbiosis_2024)}, 2025: {len(symbiosis_2025)}")

        return symbiosis_2024, symbiosis_2025

def load_and_process_data() -> Dict[str, pd.DataFrame]:
    """
    Main function to load and process all THE Impact Rankings data.

    Returns
    -------
    Dict[str, pd.DataFrame]
        Dictionary containing all processed datasets
    """
    loader = THEImpactRankingsLoader()
    df_2024, df_2025, combined_df = loader.process_datasets()
    india_2024, india_2025 = loader.get_indian_institutions()
    symbiosis_2024, symbiosis_2025 = loader.get_symbiosis_data()

    return {
        'df_2024': df_2024,
        'df_2025': df_2025,
        'combined': combined_df,
        'india_2024': india_2024,
        'india_2025': india_2025,
        'symbiosis_2024': symbiosis_2024,
        'symbiosis_2025': symbiosis_2025
    }

if __name__ == "__main__":
    # Test the data loading
    data = load_and_process_data()
    print("Data loading completed successfully!")
    for key, df in data.items():
        print(f"{key}: {len(df)} records")

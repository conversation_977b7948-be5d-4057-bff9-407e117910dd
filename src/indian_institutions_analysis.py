"""
Module: indian_institutions_analysis
Description: Comprehensive analysis module for Indian institutions in THE Impact Rankings
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- plotly
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, Tuple, List, Optional
import logging
from data_loader import load_and_process_data

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IndianInstitutionsAnalyzer:
    """
    Comprehensive analyzer for Indian institutions performance in THE Impact Rankings.
    """

    def __init__(self):
        """Initialize the analyzer with data."""
        self.data = load_and_process_data()
        self.india_2024 = self.data['india_2024'].copy()
        self.india_2025 = self.data['india_2025'].copy()
        self.df_2024 = self.data['df_2024']
        self.df_2025 = self.data['df_2025']
        self.setup_visualization_style()

    def setup_visualization_style(self):
        """Set global styling for all visualizations."""
        plt.style.use('seaborn-v0_8-darkgrid')

        # Color palettes
        self.COLOR_PALETTE = {
            'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
            'sequential': sns.color_palette("viridis", 10),
            'diverging': sns.color_palette("RdBu_r", 10),
            'india': '#ff7f0e',  # Orange for India
            'improvement': '#2ca02c',  # Green for improvements
            'decline': '#d62728'  # Red for declines
        }

        # Typography
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.titleweight'] = 'bold'
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['xtick.labelsize'] = 12
        plt.rcParams['ytick.labelsize'] = 12

        # Figure sizing
        plt.rcParams['figure.figsize'] = (14, 10)
        plt.rcParams['figure.dpi'] = 100

    def analyze_overall_performance(self) -> Dict[str, any]:
        """
        Analyze overall performance of Indian institutions.

        Returns
        -------
        Dict[str, any]
            Dictionary containing overall performance analysis
        """
        results = {}

        # Basic statistics
        results['participation'] = {
            '2024': len(self.india_2024),
            '2025': len(self.india_2025),
            'growth': len(self.india_2025) - len(self.india_2024),
            'growth_percentage': ((len(self.india_2025) - len(self.india_2024)) / len(self.india_2024)) * 100
        }

        # Ranking distribution analysis
        results['ranking_distribution'] = {
            '2024': {
                'top_100': len(self.india_2024[self.india_2024['rank_current_midpoint'] <= 100]),
                'top_200': len(self.india_2024[self.india_2024['rank_current_midpoint'] <= 200]),
                'top_500': len(self.india_2024[self.india_2024['rank_current_midpoint'] <= 500]),
                'median_rank': self.india_2024['rank_current_midpoint'].median(),
                'best_rank': self.india_2024['rank_current_midpoint'].min()
            },
            '2025': {
                'top_100': len(self.india_2025[self.india_2025['rank_current_midpoint'] <= 100]),
                'top_200': len(self.india_2025[self.india_2025['rank_current_midpoint'] <= 200]),
                'top_500': len(self.india_2025[self.india_2025['rank_current_midpoint'] <= 500]),
                'median_rank': self.india_2025['rank_current_midpoint'].median(),
                'best_rank': self.india_2025['rank_current_midpoint'].min()
            }
        }

        # Score analysis
        results['score_analysis'] = {
            '2024': {
                'mean_score': self.india_2024['score_midpoint'].mean(),
                'median_score': self.india_2024['score_midpoint'].median(),
                'std_score': self.india_2024['score_midpoint'].std(),
                'max_score': self.india_2024['score_midpoint'].max()
            },
            '2025': {
                'mean_score': self.india_2025['score_midpoint'].mean(),
                'median_score': self.india_2025['score_midpoint'].median(),
                'std_score': self.india_2025['score_midpoint'].std(),
                'max_score': self.india_2025['score_midpoint'].max()
            }
        }

        # Calculate improvements
        results['improvements'] = {
            'mean_score_change': results['score_analysis']['2025']['mean_score'] - results['score_analysis']['2024']['mean_score'],
            'median_rank_change': results['ranking_distribution']['2024']['median_rank'] - results['ranking_distribution']['2025']['median_rank'],
            'top_100_growth': results['ranking_distribution']['2025']['top_100'] - results['ranking_distribution']['2024']['top_100'],
            'top_200_growth': results['ranking_distribution']['2025']['top_200'] - results['ranking_distribution']['2024']['top_200']
        }

        logger.info(f"Overall performance analysis completed")
        logger.info(f"Indian institutions growth: {results['participation']['growth']} ({results['participation']['growth_percentage']:.1f}%)")
        logger.info(f"Top 100 institutions: {results['ranking_distribution']['2024']['top_100']} → {results['ranking_distribution']['2025']['top_100']}")

        return results

    def identify_top_performers(self, top_n: int = 20) -> Dict[str, any]:
        """
        Identify top-performing Indian institutions.

        Parameters
        ----------
        top_n : int
            Number of top institutions to analyze

        Returns
        -------
        Dict[str, any]
            Dictionary containing top performers analysis
        """
        results = {}

        # Sort institutions by ranking
        india_2024_sorted = self.india_2024.sort_values('rank_current_midpoint').head(top_n)
        india_2025_sorted = self.india_2025.sort_values('rank_current_midpoint').head(top_n)

        results['top_performers_2024'] = india_2024_sorted[['institution_name', 'rank_current', 'score_midpoint', 'rank_current_midpoint']].to_dict('records')
        results['top_performers_2025'] = india_2025_sorted[['institution_name', 'rank_current', 'score_midpoint', 'rank_current_midpoint']].to_dict('records')

        # Identify consistent top performers (appearing in both years' top lists)
        top_2024_names = set(india_2024_sorted['institution_name'].str.lower())
        top_2025_names = set(india_2025_sorted['institution_name'].str.lower())
        consistent_performers = top_2024_names.intersection(top_2025_names)

        results['consistent_top_performers'] = list(consistent_performers)
        results['new_entrants_2025'] = list(top_2025_names - top_2024_names)
        results['dropped_out_2025'] = list(top_2024_names - top_2025_names)

        # Performance trends for top institutions
        results['performance_trends'] = []
        for inst_name in consistent_performers:
            inst_2024 = self.india_2024[self.india_2024['institution_name'].str.lower() == inst_name]
            inst_2025 = self.india_2025[self.india_2025['institution_name'].str.lower() == inst_name]

            if len(inst_2024) > 0 and len(inst_2025) > 0:
                trend = {
                    'institution': inst_2024.iloc[0]['institution_name'],
                    'rank_2024': inst_2024.iloc[0]['rank_current_midpoint'],
                    'rank_2025': inst_2025.iloc[0]['rank_current_midpoint'],
                    'score_2024': inst_2024.iloc[0]['score_midpoint'],
                    'score_2025': inst_2025.iloc[0]['score_midpoint'],
                    'rank_change': inst_2024.iloc[0]['rank_current_midpoint'] - inst_2025.iloc[0]['rank_current_midpoint'],
                    'score_change': inst_2025.iloc[0]['score_midpoint'] - inst_2024.iloc[0]['score_midpoint']
                }
                results['performance_trends'].append(trend)

        logger.info(f"Top performers analysis completed")
        logger.info(f"Consistent top performers: {len(consistent_performers)}")
        logger.info(f"New entrants in 2025: {len(results['new_entrants_2025'])}")

        return results

    def analyze_growth_trends(self) -> Dict[str, any]:
        """
        Analyze growth trends and patterns in Indian institutions' performance.

        Returns
        -------
        Dict[str, any]
            Dictionary containing growth trends analysis
        """
        results = {}

        # Find institutions present in both years for trend analysis
        india_2024_names = set(self.india_2024['institution_name'].str.lower())
        india_2025_names = set(self.india_2025['institution_name'].str.lower())
        common_institutions = india_2024_names.intersection(india_2025_names)

        # Analyze trends for common institutions
        trend_data = []
        for inst_name in common_institutions:
            inst_2024 = self.india_2024[self.india_2024['institution_name'].str.lower() == inst_name]
            inst_2025 = self.india_2025[self.india_2025['institution_name'].str.lower() == inst_name]

            if len(inst_2024) > 0 and len(inst_2025) > 0:
                trend = {
                    'institution': inst_2024.iloc[0]['institution_name'],
                    'rank_2024': inst_2024.iloc[0]['rank_current_midpoint'],
                    'rank_2025': inst_2025.iloc[0]['rank_current_midpoint'],
                    'score_2024': inst_2024.iloc[0]['score_midpoint'],
                    'score_2025': inst_2025.iloc[0]['score_midpoint'],
                    'rank_change': inst_2024.iloc[0]['rank_current_midpoint'] - inst_2025.iloc[0]['rank_current_midpoint'],
                    'score_change': inst_2025.iloc[0]['score_midpoint'] - inst_2024.iloc[0]['score_midpoint']
                }
                trend_data.append(trend)

        trend_df = pd.DataFrame(trend_data)

        # Categorize performance changes
        results['performance_categories'] = {
            'improved_rank': len(trend_df[trend_df['rank_change'] > 0]),
            'declined_rank': len(trend_df[trend_df['rank_change'] < 0]),
            'stable_rank': len(trend_df[trend_df['rank_change'] == 0]),
            'improved_score': len(trend_df[trend_df['score_change'] > 0]),
            'declined_score': len(trend_df[trend_df['score_change'] < 0]),
            'stable_score': len(trend_df[trend_df['score_change'] == 0])
        }

        # Identify biggest improvers and decliners
        results['biggest_improvers'] = trend_df.nlargest(10, 'rank_change')[['institution', 'rank_change', 'score_change']].to_dict('records')
        results['biggest_decliners'] = trend_df.nsmallest(10, 'rank_change')[['institution', 'rank_change', 'score_change']].to_dict('records')

        # Score improvement analysis
        results['score_improvers'] = trend_df.nlargest(10, 'score_change')[['institution', 'rank_change', 'score_change']].to_dict('records')
        results['score_decliners'] = trend_df.nsmallest(10, 'score_change')[['institution', 'rank_change', 'score_change']].to_dict('records')

        # Overall trend statistics
        results['trend_statistics'] = {
            'total_common_institutions': len(common_institutions),
            'avg_rank_change': trend_df['rank_change'].mean(),
            'avg_score_change': trend_df['score_change'].mean(),
            'median_rank_change': trend_df['rank_change'].median(),
            'median_score_change': trend_df['score_change'].median()
        }

        results['trend_data'] = trend_data  # Store for visualization

        logger.info(f"Growth trends analysis completed")
        logger.info(f"Common institutions analyzed: {len(common_institutions)}")
        logger.info(f"Institutions with improved rankings: {results['performance_categories']['improved_rank']}")

        return results

    def create_comprehensive_visualization(self, save_path: str = "output/figures/indian_institutions_analysis.png") -> str:
        """
        Create comprehensive visualization for Indian institutions analysis.

        Parameters
        ----------
        save_path : str
            Path to save the visualization

        Returns
        -------
        str
            Path to saved visualization
        """
        # Get analysis data
        overall_perf = self.analyze_overall_performance()
        top_performers = self.identify_top_performers()
        growth_trends = self.analyze_growth_trends()

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

        fig.suptitle('Indian Institutions in THE Impact Rankings - Comprehensive Analysis',
                     fontsize=24, fontweight='bold', y=0.98)

        # 1. Participation Growth
        ax1 = fig.add_subplot(gs[0, 0])
        years = [2024, 2025]
        participation = [overall_perf['participation']['2024'], overall_perf['participation']['2025']]

        bars = ax1.bar(years, participation, color=[self.COLOR_PALETTE['categorical'][0], self.COLOR_PALETTE['india']],
                      alpha=0.8, width=0.6)
        ax1.set_title('Participation Growth', fontweight='bold', fontsize=14)
        ax1.set_xlabel('Year')
        ax1.set_ylabel('Number of Institutions')

        # Add values on bars
        for bar, value in zip(bars, participation):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    str(value), ha='center', va='bottom', fontweight='bold')

        growth = overall_perf['participation']['growth']
        ax1.text(2024.5, max(participation) + 10, f'+{growth} institutions\n(+{overall_perf["participation"]["growth_percentage"]:.1f}%)',
                ha='center', va='bottom', fontsize=11, color='green', fontweight='bold')

        # 2. Top Tier Performance
        ax2 = fig.add_subplot(gs[0, 1])
        categories = ['Top 100', 'Top 200', 'Top 500']
        values_2024 = [overall_perf['ranking_distribution']['2024']['top_100'],
                      overall_perf['ranking_distribution']['2024']['top_200'],
                      overall_perf['ranking_distribution']['2024']['top_500']]
        values_2025 = [overall_perf['ranking_distribution']['2025']['top_100'],
                      overall_perf['ranking_distribution']['2025']['top_200'],
                      overall_perf['ranking_distribution']['2025']['top_500']]

        x = np.arange(len(categories))
        width = 0.35

        bars1 = ax2.bar(x - width/2, values_2024, width, label='2024', color=self.COLOR_PALETTE['categorical'][0], alpha=0.8)
        bars2 = ax2.bar(x + width/2, values_2025, width, label='2025', color=self.COLOR_PALETTE['india'], alpha=0.8)

        ax2.set_title('Top Tier Performance', fontweight='bold', fontsize=14)
        ax2.set_xlabel('Ranking Categories')
        ax2.set_ylabel('Number of Institutions')
        ax2.set_xticks(x)
        ax2.set_xticklabels(categories)
        ax2.legend()

        # Add values on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2, height + 0.1,
                        f'{int(height)}', ha='center', va='bottom', fontsize=10)

        # 3. Score Distribution
        ax3 = fig.add_subplot(gs[0, 2])

        # Create score distribution comparison
        ax3.hist(self.india_2024['score_midpoint'], bins=20, alpha=0.6, label='2024',
                color=self.COLOR_PALETTE['categorical'][0], density=True)
        ax3.hist(self.india_2025['score_midpoint'], bins=20, alpha=0.6, label='2025',
                color=self.COLOR_PALETTE['india'], density=True)

        ax3.set_title('Score Distribution', fontweight='bold', fontsize=14)
        ax3.set_xlabel('Score')
        ax3.set_ylabel('Density')
        ax3.legend()

        # 4. Performance Trends (Rank Changes)
        ax4 = fig.add_subplot(gs[1, :2])

        trend_data = pd.DataFrame(growth_trends['trend_data'])
        if len(trend_data) > 0:
            # Create scatter plot of rank changes vs score changes
            colors = ['green' if x > 0 else 'red' if x < 0 else 'gray' for x in trend_data['rank_change']]
            scatter = ax4.scatter(trend_data['score_change'], trend_data['rank_change'],
                                c=colors, alpha=0.6, s=50)

            ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            ax4.axvline(x=0, color='black', linestyle='--', alpha=0.5)

            ax4.set_title('Performance Trends: Rank Change vs Score Change', fontweight='bold', fontsize=14)
            ax4.set_xlabel('Score Change')
            ax4.set_ylabel('Rank Change (Positive = Improvement)')

            # Add quadrant labels
            ax4.text(0.02, 0.98, 'Improved Rank\nLower Score', transform=ax4.transAxes,
                    ha='left', va='top', fontsize=10, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
            ax4.text(0.98, 0.98, 'Improved Rank\nHigher Score', transform=ax4.transAxes,
                    ha='right', va='top', fontsize=10, bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
            ax4.text(0.02, 0.02, 'Declined Rank\nLower Score', transform=ax4.transAxes,
                    ha='left', va='bottom', fontsize=10, bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.5))
            ax4.text(0.98, 0.02, 'Declined Rank\nHigher Score', transform=ax4.transAxes,
                    ha='right', va='bottom', fontsize=10, bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.5))

        # 5. Top Performers Comparison
        ax5 = fig.add_subplot(gs[1, 2])

        # Performance categories pie chart
        categories = ['Improved Rank', 'Declined Rank', 'Stable Rank']
        values = [growth_trends['performance_categories']['improved_rank'],
                 growth_trends['performance_categories']['declined_rank'],
                 growth_trends['performance_categories']['stable_rank']]
        colors = [self.COLOR_PALETTE['improvement'], self.COLOR_PALETTE['decline'], 'gray']

        wedges, texts, autotexts = ax5.pie(values, labels=categories, colors=colors, autopct='%1.1f%%',
                                          startangle=90)
        ax5.set_title('Rank Change Distribution', fontweight='bold', fontsize=14)

        # 6. Top 10 Institutions Comparison
        ax6 = fig.add_subplot(gs[2, :])

        # Get top 10 institutions from 2025
        top_10_2025 = self.india_2025.nsmallest(10, 'rank_current_midpoint')

        # Create horizontal bar chart
        y_pos = np.arange(len(top_10_2025))
        institutions = [name[:30] + '...' if len(name) > 30 else name for name in top_10_2025['institution_name']]
        ranks = top_10_2025['rank_current_midpoint']
        scores = top_10_2025['score_midpoint']

        # Create twin axis for scores
        bars = ax6.barh(y_pos, ranks, color=self.COLOR_PALETTE['india'], alpha=0.7)
        ax6.set_yticks(y_pos)
        ax6.set_yticklabels(institutions, fontsize=10)
        ax6.set_xlabel('Global Ranking Position')
        ax6.set_title('Top 10 Indian Institutions in THE Impact Rankings 2025', fontweight='bold', fontsize=14)
        ax6.invert_xaxis()  # Lower rank number is better

        # Add rank values on bars
        for i, (bar, rank) in enumerate(zip(bars, ranks)):
            ax6.text(rank - 5, bar.get_y() + bar.get_height()/2,
                    f'{int(rank)}', ha='right', va='center', fontweight='bold', color='white')

        # Add scores as text
        ax6_twin = ax6.twiny()
        ax6_twin.set_xlim(ax6.get_xlim())
        for i, score in enumerate(scores):
            ax6_twin.text(0.95, i, f'Score: {score:.1f}', transform=ax6_twin.get_yaxis_transform(),
                         ha='right', va='center', fontsize=9, color='blue')
        ax6_twin.set_xticks([])

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Comprehensive visualization saved to {save_path}")
        return save_path

    def generate_comprehensive_report(self) -> Dict[str, any]:
        """
        Generate comprehensive report for Indian institutions analysis.

        Returns
        -------
        Dict[str, any]
            Complete analysis report
        """
        report = {
            'title': 'Indian Institutions in THE Impact Rankings - Comprehensive Analysis',
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d'),
            'overall_performance': self.analyze_overall_performance(),
            'top_performers': self.identify_top_performers(),
            'growth_trends': self.analyze_growth_trends()
        }

        # Generate executive summary
        overall = report['overall_performance']
        trends = report['growth_trends']

        executive_summary = []

        # Participation insights
        participation_growth = overall['participation']['growth']
        growth_pct = overall['participation']['growth_percentage']
        executive_summary.append(f"Indian participation in THE Impact Rankings increased by {participation_growth} institutions ({growth_pct:.1f}%) from 2024 to 2025")

        # Performance insights
        top_100_change = overall['improvements']['top_100_growth']
        if top_100_change > 0:
            executive_summary.append(f"Number of Indian institutions in top 100 increased by {top_100_change}")
        elif top_100_change < 0:
            executive_summary.append(f"Number of Indian institutions in top 100 decreased by {abs(top_100_change)}")
        else:
            executive_summary.append("Number of Indian institutions in top 100 remained stable")

        # Trend insights
        improved_count = trends['performance_categories']['improved_rank']
        total_common = trends['trend_statistics']['total_common_institutions']
        improvement_pct = (improved_count / total_common) * 100 if total_common > 0 else 0

        executive_summary.append(f"{improved_count} out of {total_common} continuing institutions ({improvement_pct:.1f}%) improved their rankings")

        # Best performer insight
        best_rank_2025 = overall['ranking_distribution']['2025']['best_rank']
        executive_summary.append(f"Best Indian institution achieved rank {int(best_rank_2025)} globally in 2025")

        report['executive_summary'] = executive_summary

        # Generate key insights
        insights = []

        # Growth insights
        if growth_pct > 20:
            insights.append("Significant growth in Indian institutional participation demonstrates increasing focus on sustainability and impact")

        # Performance insights
        avg_score_change = overall['improvements']['mean_score_change']
        if avg_score_change > 0:
            insights.append(f"Average score improvement of {avg_score_change:.2f} points indicates overall enhancement in sustainability practices")

        # Competitive insights
        median_rank_change = overall['improvements']['median_rank_change']
        if median_rank_change > 0:
            insights.append("Median ranking improvement suggests broad-based enhancement across Indian institutions")

        # Top performer insights
        consistent_performers = len(report['top_performers']['consistent_top_performers'])
        insights.append(f"{consistent_performers} institutions maintained top performance across both years, showing sustained excellence")

        report['key_insights'] = insights

        # Strategic recommendations
        recommendations = [
            "Establish national coordination mechanism for THE Impact Rankings preparation and best practice sharing",
            "Create mentorship programs pairing top-performing institutions with emerging ones",
            "Develop standardized sustainability reporting frameworks aligned with THE Impact Rankings criteria",
            "Invest in research infrastructure supporting UN SDG-aligned projects and initiatives",
            "Strengthen international partnerships to enhance global visibility and collaboration opportunities",
            "Implement comprehensive data collection systems for accurate impact measurement and reporting",
            "Focus on areas where Indian institutions lag behind global leaders for targeted improvement",
            "Develop capacity building programs for institutional research and sustainability teams"
        ]

        report['strategic_recommendations'] = recommendations

        return report

def run_indian_institutions_analysis():
    """Main function to run complete Indian institutions analysis."""
    analyzer = IndianInstitutionsAnalyzer()

    # Generate comprehensive report
    report = analyzer.generate_comprehensive_report()

    # Create visualization
    viz_path = analyzer.create_comprehensive_visualization()

    # Print summary
    print("="*100)
    print("INDIAN INSTITUTIONS IN THE IMPACT RANKINGS - COMPREHENSIVE ANALYSIS")
    print("="*100)

    overall = report['overall_performance']
    trends = report['growth_trends']
    top_perf = report['top_performers']

    print(f"\n📊 EXECUTIVE SUMMARY:")
    for summary in report['executive_summary']:
        print(f"   • {summary}")

    print(f"\n📈 PARTICIPATION & GROWTH:")
    print(f"   • 2024: {overall['participation']['2024']} institutions")
    print(f"   • 2025: {overall['participation']['2025']} institutions")
    print(f"   • Growth: +{overall['participation']['growth']} institutions ({overall['participation']['growth_percentage']:.1f}%)")

    print(f"\n🏆 TOP TIER PERFORMANCE:")
    print(f"   • Top 100: {overall['ranking_distribution']['2024']['top_100']} → {overall['ranking_distribution']['2025']['top_100']} (Change: {overall['improvements']['top_100_growth']:+d})")
    print(f"   • Top 200: {overall['ranking_distribution']['2024']['top_200']} → {overall['ranking_distribution']['2025']['top_200']} (Change: {overall['improvements']['top_200_growth']:+d})")
    print(f"   • Best Rank 2025: {int(overall['ranking_distribution']['2025']['best_rank'])}")

    print(f"\n📊 PERFORMANCE TRENDS:")
    print(f"   • Institutions analyzed: {trends['trend_statistics']['total_common_institutions']}")
    print(f"   • Improved rankings: {trends['performance_categories']['improved_rank']}")
    print(f"   • Declined rankings: {trends['performance_categories']['declined_rank']}")
    print(f"   • Average rank change: {trends['trend_statistics']['avg_rank_change']:+.1f}")
    print(f"   • Average score change: {trends['trend_statistics']['avg_score_change']:+.2f}")

    print(f"\n🌟 TOP PERFORMERS:")
    print("   Top 5 Indian Institutions in 2025:")
    for i, inst in enumerate(report['top_performers']['top_performers_2025'][:5], 1):
        print(f"   {i}. {inst['institution_name']} - Rank: {inst['rank_current']}, Score: {inst['score_midpoint']:.1f}")

    print(f"\n💡 KEY INSIGHTS:")
    for insight in report['key_insights']:
        print(f"   • {insight}")

    print(f"\n🎯 STRATEGIC RECOMMENDATIONS:")
    for i, rec in enumerate(report['strategic_recommendations'][:5], 1):  # Show first 5
        print(f"   {i}. {rec}")

    print(f"\n📈 Comprehensive visualization saved to: {viz_path}")
    print("="*100)

    return report

if __name__ == "__main__":
    run_indian_institutions_analysis()
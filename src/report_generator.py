"""
Module: report_generator
Description: Comprehensive report generator for THE Impact Rankings analysis
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- jinja2
- weasyprint
"""

import pandas as pd
import os
from datetime import datetime
from typing import Dict, List, Any
import logging
import json

from data_loader import load_and_process_data
from symbiosis_analysis import SymbiosisAnalyzer
from indian_institutions_analysis import IndianInstitutionsAnalyzer
from global_benchmarking import GlobalBenchmarkingAnalyzer
from strategic_insights import StrategicInsightsGenerator
from visualization_suite import VisualizationSuite

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveReportGenerator:
    """
    Comprehensive report generator for THE Impact Rankings analysis.
    """

    def __init__(self):
        """Initialize the report generator."""
        self.data = load_and_process_data()
        self.symbiosis_analyzer = SymbiosisAnalyzer()
        self.indian_analyzer = IndianInstitutionsAnalyzer()
        self.global_analyzer = GlobalBenchmarkingAnalyzer()
        self.strategic_generator = StrategicInsightsGenerator()
        self.visualization_suite = VisualizationSuite()

        # Generate all analysis results
        self.symbiosis_results = self.symbiosis_analyzer.generate_performance_report()
        self.indian_results = self.indian_analyzer.generate_comprehensive_report()
        self.global_results = self.global_analyzer.generate_benchmarking_report()
        self.strategic_results = self.strategic_generator.generate_comprehensive_strategic_report()

    def generate_executive_summary(self) -> Dict[str, Any]:
        """Generate executive summary for the comprehensive report."""
        # Key metrics
        symbiosis_improvement = self.symbiosis_results['performance_trends']['rank_improvement']
        indian_growth = self.indian_results['overall_performance']['participation']['growth_percentage']
        best_indian_rank = self.data['india_2025']['rank_current_midpoint'].min()
        top_100_growth = self.indian_results['overall_performance']['improvements']['top_100_growth']

        summary = {
            'key_findings': [
                f"Symbiosis International University achieved remarkable improvement of {symbiosis_improvement:.0f} positions in THE Impact Rankings 2025",
                f"Indian higher education participation grew by {indian_growth:.1f}% with {self.indian_results['overall_performance']['participation']['growth']} new institutions",
                f"India's best-performing institution reached rank {int(best_indian_rank)}, demonstrating significant progress",
                f"Number of Indian institutions in global top 100 increased by {top_100_growth}, showing enhanced competitiveness",
                "Strategic recommendations provide clear roadmap for sustained improvement and global leadership"
            ],
            'performance_highlights': {
                'symbiosis_rank_improvement': f"{symbiosis_improvement:.0f} positions",
                'symbiosis_score_improvement': f"{self.symbiosis_results['performance_trends']['score_improvement']:.2f} points",
                'indian_institutions_growth': f"{self.indian_results['overall_performance']['participation']['growth']} institutions",
                'best_indian_global_rank': f"#{int(best_indian_rank)}",
                'top_100_institutions': f"{self.indian_results['overall_performance']['ranking_distribution']['2025']['top_100']} institutions"
            },
            'strategic_priorities': [
                "Research excellence enhancement through interdisciplinary sustainability programs",
                "International collaboration development with top global institutions",
                "Comprehensive sustainability infrastructure implementation",
                "National coordination mechanism establishment for sector-wide improvement",
                "Data systems and impact measurement framework development"
            ]
        }

        return summary

    def generate_detailed_findings(self) -> Dict[str, Any]:
        """Generate detailed findings section."""
        findings = {
            'symbiosis_analysis': {
                'title': 'Symbiosis International University Performance Analysis',
                'current_position': {
                    '2024_rank': self.symbiosis_results['performance_trends']['rank_2024']['original'],
                    '2025_rank': self.symbiosis_results['performance_trends']['rank_2025']['original'],
                    'improvement': f"{self.symbiosis_results['performance_trends']['rank_improvement']:.0f} positions",
                    'national_position_2025': f"{self.symbiosis_results['national_position']['national_position']['2025']['rank_among_indian']}/{self.symbiosis_results['national_position']['national_position']['2025']['total_indian_institutions']}"
                },
                'key_achievements': [
                    f"Moved from rank band {self.symbiosis_results['performance_trends']['rank_2024']['original']} to {self.symbiosis_results['performance_trends']['rank_2025']['original']}",
                    f"Score improved by {self.symbiosis_results['performance_trends']['score_improvement']:.2f} points",
                    f"National ranking improved by {self.symbiosis_results['national_position']['national_improvement']} positions",
                    f"Now in top {100-self.symbiosis_results['national_position']['national_position']['2025']['percentile']:.1f}% of Indian institutions"
                ]
            },
            'indian_sector_analysis': {
                'title': 'Indian Institutions Comprehensive Analysis',
                'participation_growth': {
                    '2024_count': self.indian_results['overall_performance']['participation']['2024'],
                    '2025_count': self.indian_results['overall_performance']['participation']['2025'],
                    'growth_absolute': self.indian_results['overall_performance']['participation']['growth'],
                    'growth_percentage': f"{self.indian_results['overall_performance']['participation']['growth_percentage']:.1f}%"
                },
                'performance_improvements': {
                    'top_100_institutions': f"{self.indian_results['overall_performance']['ranking_distribution']['2024']['top_100']} → {self.indian_results['overall_performance']['ranking_distribution']['2025']['top_100']}",
                    'top_200_institutions': f"{self.indian_results['overall_performance']['ranking_distribution']['2024']['top_200']} → {self.indian_results['overall_performance']['ranking_distribution']['2025']['top_200']}",
                    'best_rank_2025': int(self.indian_results['overall_performance']['ranking_distribution']['2025']['best_rank']),
                    'institutions_improved': self.indian_results['growth_trends']['performance_categories']['improved_rank']
                }
            },
            'global_benchmarking': {
                'title': 'Global Benchmarking and Competitive Analysis',
                'india_global_position': {
                    'best_rank': int(self.global_results['india_comparison']['competitive_position']['2025']['institutions_ahead_of_best_indian']) + 1,
                    'gap_to_top_10': self.global_results['india_comparison']['performance_gaps']['2025']['rank_gap_to_top_10'],
                    'gap_to_top_25': self.global_results['india_comparison']['performance_gaps']['2025']['rank_gap_to_top_25'],
                    'improvement_trajectory': self.global_results['india_comparison']['progress_analysis']['rank_gap_improvement']
                },
                'regional_competition': {
                    'asian_ranking': self._get_india_asian_ranking(),
                    'leading_asian_countries': [country for country, _ in self.global_results['regional_analysis']['regional_rankings_2025']['best_rank'][:5]]
                }
            }
        }

        return findings

    def _get_india_asian_ranking(self) -> int:
        """Get India's ranking among Asian countries."""
        regional_rankings = self.global_results['regional_analysis']['regional_rankings_2025']['best_rank']
        for i, (country, _) in enumerate(regional_rankings):
            if country == 'India':
                return i + 1
        return len(regional_rankings)  # If not found, return last position

    def generate_html_report(self, save_path: str = "output/reports/comprehensive_report.html") -> str:
        """Generate comprehensive HTML report."""

        # Get all data
        executive_summary = self.generate_executive_summary()
        detailed_findings = self.generate_detailed_findings()

        # Create HTML content
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>THE Impact Rankings 2024-2025: Comprehensive Analysis Report</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {{ font-family: 'Arial', sans-serif; }}
        .gradient-bg {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }}
        .card-shadow {{ box-shadow: 0 10px 25px rgba(0,0,0,0.1); }}
        .metric-card {{ transition: transform 0.3s ease; }}
        .metric-card:hover {{ transform: translateY(-5px); }}
        .section-divider {{ border-top: 3px solid #667eea; }}
        @media print {{ .no-print {{ display: none; }} }}
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-12">
        <div class="container mx-auto px-6">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    THE Impact Rankings 2024-2025
                </h1>
                <h2 class="text-2xl md:text-3xl font-light mb-6">
                    Comprehensive Analysis Report
                </h2>
                <div class="bg-white bg-opacity-20 rounded-lg p-4 inline-block">
                    <p class="text-lg">
                        <i class="fas fa-university mr-2"></i>
                        Symbiosis International University & Indian Higher Education Analysis
                    </p>
                    <p class="text-sm mt-2">
                        <i class="fas fa-calendar mr-2"></i>
                        Report Generated: {datetime.now().strftime('%B %d, %Y')}
                    </p>
                </div>
            </div>
        </div>
    </header>

    <!-- Executive Summary -->
    <section class="py-12">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-blue-600 mr-3"></i>
                    Executive Summary
                </h2>
                <div class="w-24 h-1 bg-blue-600 mx-auto"></div>
            </div>

            <!-- Key Performance Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
                <div class="metric-card bg-white rounded-lg p-6 card-shadow text-center">
                    <div class="text-3xl font-bold text-green-600 mb-2">
                        +{executive_summary['performance_highlights']['symbiosis_rank_improvement']}
                    </div>
                    <div class="text-gray-600 text-sm">Symbiosis Rank Improvement</div>
                </div>
                <div class="metric-card bg-white rounded-lg p-6 card-shadow text-center">
                    <div class="text-3xl font-bold text-blue-600 mb-2">
                        +{executive_summary['performance_highlights']['indian_institutions_growth']}
                    </div>
                    <div class="text-gray-600 text-sm">New Indian Institutions</div>
                </div>
                <div class="metric-card bg-white rounded-lg p-6 card-shadow text-center">
                    <div class="text-3xl font-bold text-purple-600 mb-2">
                        {executive_summary['performance_highlights']['best_indian_global_rank']}
                    </div>
                    <div class="text-gray-600 text-sm">Best Indian Global Rank</div>
                </div>
                <div class="metric-card bg-white rounded-lg p-6 card-shadow text-center">
                    <div class="text-3xl font-bold text-orange-600 mb-2">
                        {executive_summary['performance_highlights']['top_100_institutions']}
                    </div>
                    <div class="text-gray-600 text-sm">Indian Institutions in Top 100</div>
                </div>
                <div class="metric-card bg-white rounded-lg p-6 card-shadow text-center">
                    <div class="text-3xl font-bold text-red-600 mb-2">
                        +{executive_summary['performance_highlights']['symbiosis_score_improvement']}
                    </div>
                    <div class="text-gray-600 text-sm">Symbiosis Score Improvement</div>
                </div>
            </div>

            <!-- Key Findings -->
            <div class="bg-white rounded-lg p-8 card-shadow">
                <h3 class="text-2xl font-bold text-gray-800 mb-6">
                    <i class="fas fa-lightbulb text-yellow-500 mr-3"></i>
                    Key Findings
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <ul class="space-y-4">
                            {self._generate_findings_list(executive_summary['key_findings'][:3])}
                        </ul>
                    </div>
                    <div>
                        <ul class="space-y-4">
                            {self._generate_findings_list(executive_summary['key_findings'][3:])}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Detailed Analysis Sections -->
    <section class="py-12 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-microscope text-blue-600 mr-3"></i>
                    Detailed Analysis
                </h2>
                <div class="w-24 h-1 bg-blue-600 mx-auto"></div>
            </div>

            <!-- Symbiosis Analysis -->
            <div class="mb-12">
                <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-t-lg p-6">
                    <h3 class="text-2xl font-bold">
                        <i class="fas fa-university mr-3"></i>
                        {detailed_findings['symbiosis_analysis']['title']}
                    </h3>
                </div>
                <div class="bg-gray-50 rounded-b-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-800">
                                {detailed_findings['symbiosis_analysis']['current_position']['2024_rank']}
                            </div>
                            <div class="text-sm text-gray-600">2024 Rank</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">
                                {detailed_findings['symbiosis_analysis']['current_position']['2025_rank']}
                            </div>
                            <div class="text-sm text-gray-600">2025 Rank</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">
                                +{detailed_findings['symbiosis_analysis']['current_position']['improvement']}
                            </div>
                            <div class="text-sm text-gray-600">Improvement</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">
                                {detailed_findings['symbiosis_analysis']['current_position']['national_position_2025']}
                            </div>
                            <div class="text-sm text-gray-600">National Position</div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg p-6">
                        <h4 class="text-lg font-bold text-gray-800 mb-4">Key Achievements</h4>
                        <ul class="space-y-2">
                            {self._generate_achievements_list(detailed_findings['symbiosis_analysis']['key_achievements'])}
                        </ul>
                    </div>
                </div>
            </div>
        """

        # Ensure output directory exists
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Save HTML report
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Comprehensive HTML report saved to {save_path}")
        return save_path


    def _generate_findings_list(self, findings: List[str]) -> str:
        """Generate HTML list for findings."""
        html_items = []
        for finding in findings:
            html_items.append(f"""
                <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mr-3 mt-1"></i>
                    <span class="text-gray-700">{finding}</span>
                </li>
            """)
        return "".join(html_items)

    def _generate_achievements_list(self, achievements: List[str]) -> str:
        """Generate HTML list for achievements."""
        html_items = []
        for achievement in achievements:
            html_items.append(f"""
                <li class="flex items-start">
                    <i class="fas fa-star text-yellow-500 mr-3 mt-1"></i>
                    <span class="text-gray-700">{achievement}</span>
                </li>
            """)
        return "".join(html_items)

    def _generate_country_badges(self, countries: List[str]) -> str:
        """Generate HTML badges for countries."""
        badges = []
        for country in countries:
            badges.append(f"""
                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    {country}
                </span>
            """)
        return "".join(badges)

    def _generate_recommendations_list(self, recommendations: List[str]) -> str:
        """Generate HTML list for recommendations."""
        html_items = []
        for rec in recommendations:
            html_items.append(f"""
                <div class="flex items-start">
                    <i class="fas fa-arrow-right text-blue-500 mr-3 mt-1"></i>
                    <span class="text-gray-700">{rec}</span>
                </div>
            """)
        return "".join(html_items)

    def generate_data_export(self, save_path: str = "output/reports/analysis_data.json") -> str:
        """Export all analysis data to JSON format."""
        export_data = {
            'metadata': {
                'report_title': 'THE Impact Rankings 2024-2025 Comprehensive Analysis',
                'generated_date': datetime.now().isoformat(),
                'author': 'Dr. Dharmendra Pandey',
                'institution': 'Symbiosis International (Deemed University)'
            },
            'executive_summary': self.generate_executive_summary(),
            'detailed_findings': self.generate_detailed_findings(),
            'symbiosis_analysis': self.symbiosis_results,
            'indian_sector_analysis': self.indian_results,
            'global_benchmarking': self.global_results,
            'strategic_recommendations': self.strategic_results,
            'data_summary': {
                'total_institutions_2024': len(self.data['df_2024']),
                'total_institutions_2025': len(self.data['df_2025']),
                'indian_institutions_2024': len(self.data['india_2024']),
                'indian_institutions_2025': len(self.data['india_2025']),
                'symbiosis_institutions': {
                    '2024': len(self.data['symbiosis_2024']),
                    '2025': len(self.data['symbiosis_2025'])
                }
            }
        }

        # Ensure output directory exists
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # Save JSON data
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"Analysis data exported to {save_path}")
        return save_path

    def generate_complete_report_package(self) -> Dict[str, str]:
        """Generate complete report package with all components."""
        package = {}

        logger.info("Generating comprehensive report package...")

        # Generate all visualizations
        logger.info("Creating visualization suite...")
        visualizations = self.visualization_suite.create_comprehensive_report_visualizations()
        package.update(visualizations)

        # Generate HTML report
        logger.info("Generating HTML report...")
        package['html_report'] = self.generate_html_report()

        # Export data
        logger.info("Exporting analysis data...")
        package['data_export'] = self.generate_data_export()

        # Generate summary tables
        logger.info("Creating summary tables...")
        package['summary_tables'] = self._create_summary_tables()

        return package

    def _create_summary_tables(self) -> str:
        """Create summary tables in CSV format."""
        save_path = "output/tables/summary_tables.csv"

        # Create summary data
        summary_data = []

        # Symbiosis performance
        symbiosis_perf = self.symbiosis_results['performance_trends']
        summary_data.append({
            'Institution': 'Symbiosis International University',
            'Type': 'Individual Institution',
            'Metric': 'Global Rank 2024',
            'Value': symbiosis_perf['rank_2024']['original']
        })
        summary_data.append({
            'Institution': 'Symbiosis International University',
            'Type': 'Individual Institution',
            'Metric': 'Global Rank 2025',
            'Value': symbiosis_perf['rank_2025']['original']
        })
        summary_data.append({
            'Institution': 'Symbiosis International University',
            'Type': 'Individual Institution',
            'Metric': 'Rank Improvement',
            'Value': f"+{symbiosis_perf['rank_improvement']:.0f} positions"
        })

        # Indian sector performance
        indian_perf = self.indian_results['overall_performance']
        summary_data.append({
            'Institution': 'Indian Higher Education Sector',
            'Type': 'Sector Analysis',
            'Metric': 'Total Institutions 2024',
            'Value': indian_perf['participation']['2024']
        })
        summary_data.append({
            'Institution': 'Indian Higher Education Sector',
            'Type': 'Sector Analysis',
            'Metric': 'Total Institutions 2025',
            'Value': indian_perf['participation']['2025']
        })
        summary_data.append({
            'Institution': 'Indian Higher Education Sector',
            'Type': 'Sector Analysis',
            'Metric': 'Growth Rate',
            'Value': f"{indian_perf['participation']['growth_percentage']:.1f}%"
        })

        # Create DataFrame and save
        df_summary = pd.DataFrame(summary_data)

        # Ensure output directory exists
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        df_summary.to_csv(save_path, index=False)

        logger.info(f"Summary tables saved to {save_path}")
        return save_path

def run_comprehensive_report_generation():
    """Main function to generate comprehensive report."""
    generator = ComprehensiveReportGenerator()

    print("="*100)
    print("COMPREHENSIVE REPORT GENERATION - THE IMPACT RANKINGS ANALYSIS")
    print("="*100)
    print("\nGenerating comprehensive report package...")
    print("This may take several minutes to complete all components...")

    # Generate complete package
    package = generator.generate_complete_report_package()

    # Print summary
    print(f"\n📊 REPORT PACKAGE GENERATED SUCCESSFULLY")
    print(f"\n📁 Generated Files:")

    file_descriptions = {
        'html_report': '📄 Comprehensive HTML Report - Main report document',
        'data_export': '📊 Analysis Data Export - JSON format for further analysis',
        'summary_tables': '📋 Summary Tables - CSV format for quick reference',
        'executive_dashboard': '📈 Executive Dashboard - Key metrics visualization',
        'interactive_dashboard': '🖥️ Interactive Dashboard - Dynamic analysis tool',
        'symbiosis_analysis': '🏛️ Symbiosis Analysis - Detailed institutional performance',
        'indian_institutions': '🇮🇳 Indian Institutions Analysis - Sector-wide analysis',
        'global_benchmarking': '🌍 Global Benchmarking - International comparison'
    }

    for component, path in package.items():
        description = file_descriptions.get(component, f'{component} - Analysis component')
        print(f"   {description}")
        print(f"      📁 {path}")
        print()

    print(f"🎯 USAGE RECOMMENDATIONS:")
    print(f"   • Start with the HTML report for comprehensive overview")
    print(f"   • Use interactive dashboard for detailed exploration")
    print(f"   • Reference individual analysis charts for specific insights")
    print(f"   • Export data for further analysis and modeling")
    print(f"   • Share visualizations in presentations and reports")

    print(f"\n✅ REPORT GENERATION COMPLETED SUCCESSFULLY")
    print("="*100)

    return package

if __name__ == "__main__":
    run_comprehensive_report_generation()
"""
Module: visualization_suite
Description: Comprehensive visualization suite for THE Impact Rankings analysis
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-18
Last Modified: 2025-01-18

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- plotly
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.offline as pyo
from typing import Dict, Tuple, List, Optional
import logging
import os

from data_loader import load_and_process_data
from symbiosis_analysis import SymbiosisAnalyzer
from indian_institutions_analysis import IndianInstitutionsAnalyzer
from global_benchmarking import GlobalBenchmarkingAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VisualizationSuite:
    """
    Comprehensive visualization suite for THE Impact Rankings analysis.
    """

    def __init__(self):
        """Initialize the visualization suite."""
        self.data = load_and_process_data()
        self.symbiosis_analyzer = SymbiosisAnalyzer()
        self.indian_analyzer = IndianInstitutionsAnalyzer()
        self.global_analyzer = GlobalBenchmarkingAnalyzer()
        self.setup_styling()

    def setup_styling(self):
        """Set up consistent styling across all visualizations."""
        # Matplotlib styling
        plt.style.use('seaborn-v0_8-darkgrid')

        self.COLOR_PALETTE = {
            'primary': '#1f77b4',
            'secondary': '#ff7f0e',
            'success': '#2ca02c',
            'danger': '#d62728',
            'warning': '#ff7f0e',
            'info': '#17becf',
            'symbiosis': '#ff7f0e',
            'india': '#ff7f0e',
            'global': '#2ca02c',
            'improvement': '#2ca02c',
            'decline': '#d62728'
        }

        # Plotly theme
        self.plotly_theme = {
            'layout': {
                'font': {'family': 'Arial, sans-serif', 'size': 12},
                'title': {'font': {'size': 18, 'family': 'Arial, sans-serif'}},
                'colorway': list(self.COLOR_PALETTE.values()),
                'plot_bgcolor': 'white',
                'paper_bgcolor': 'white'
            }
        }

        # Typography
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['axes.titleweight'] = 'bold'
        plt.rcParams['figure.figsize'] = (14, 10)
        plt.rcParams['figure.dpi'] = 100

    def create_executive_dashboard(self, save_path: str = "output/figures/executive_dashboard.png") -> str:
        """
        Create executive dashboard with key metrics and insights.

        Parameters
        ----------
        save_path : str
            Path to save the dashboard

        Returns
        -------
        str
            Path to saved dashboard
        """
        # Get key data
        symbiosis_data = self.symbiosis_analyzer.analyze_performance_trends()
        indian_data = self.indian_analyzer.analyze_overall_performance()
        global_data = self.global_analyzer.compare_india_with_global_leaders()

        # Create figure
        fig = plt.figure(figsize=(20, 14))
        gs = fig.add_gridspec(3, 4, hspace=0.4, wspace=0.3)

        # Main title
        fig.suptitle('THE Impact Rankings 2024-2025: Executive Dashboard\nSymbiosis International University & Indian Institutions Analysis',
                     fontsize=24, fontweight='bold', y=0.98)

        # 1. Key Performance Indicators (Top row)
        ax1 = fig.add_subplot(gs[0, 0])
        self._create_kpi_card(ax1, "Symbiosis Rank\nImprovement", f"+{symbiosis_data['rank_improvement']:.0f}",
                             "positions", self.COLOR_PALETTE['success'])

        ax2 = fig.add_subplot(gs[0, 1])
        self._create_kpi_card(ax2, "Indian Institutions\nGrowth", f"+{indian_data['participation']['growth']}",
                             f"({indian_data['participation']['growth_percentage']:.1f}%)", self.COLOR_PALETTE['info'])

        ax3 = fig.add_subplot(gs[0, 2])
        best_indian_rank = self.data['india_2025']['rank_current_midpoint'].min()
        self._create_kpi_card(ax3, "Best Indian\nGlobal Rank", f"#{int(best_indian_rank)}",
                             "in 2025", self.COLOR_PALETTE['primary'])

        ax4 = fig.add_subplot(gs[0, 3])
        top_100_growth = indian_data['improvements']['top_100_growth']
        self._create_kpi_card(ax4, "Top 100\nInstitutions", f"+{top_100_growth}",
                             "new entries", self.COLOR_PALETTE['warning'])

        # 2. Symbiosis Performance Trend (Middle left)
        ax5 = fig.add_subplot(gs[1, :2])
        years = [2024, 2025]
        symbiosis_ranks = [symbiosis_data['rank_2024']['midpoint'], symbiosis_data['rank_2025']['midpoint']]
        symbiosis_scores = [symbiosis_data['score_2024']['midpoint'], symbiosis_data['score_2025']['midpoint']]

        ax5_twin = ax5.twinx()

        line1 = ax5.plot(years, symbiosis_ranks, marker='o', linewidth=4, markersize=10,
                        color=self.COLOR_PALETTE['symbiosis'], label='Ranking Position')
        line2 = ax5_twin.plot(years, symbiosis_scores, marker='s', linewidth=4, markersize=10,
                             color=self.COLOR_PALETTE['success'], label='Score')

        ax5.set_xlabel('Year', fontsize=14)
        ax5.set_ylabel('Ranking Position', fontsize=14, color=self.COLOR_PALETTE['symbiosis'])
        ax5_twin.set_ylabel('Score', fontsize=14, color=self.COLOR_PALETTE['success'])
        ax5.set_title('Symbiosis International University Performance Trend', fontsize=16, fontweight='bold')
        ax5.invert_yaxis()  # Lower rank is better

        # Add improvement annotation
        ax5.annotate(f'Improved by\n{symbiosis_data["rank_improvement"]:.0f} positions',
                    xy=(2025, symbiosis_ranks[1]), xytext=(2024.5, symbiosis_ranks[1] - 50),
                    arrowprops=dict(arrowstyle='->', color='green', lw=3),
                    fontsize=12, ha='center', color='green', fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))

        # Combine legends
        lines1, labels1 = ax5.get_legend_handles_labels()
        lines2, labels2 = ax5_twin.get_legend_handles_labels()
        ax5.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 3. Indian Institutions Growth (Middle right)
        ax6 = fig.add_subplot(gs[1, 2:])

        categories = ['Total\nInstitutions', 'Top 100', 'Top 200', 'Top 500']
        values_2024 = [indian_data['participation']['2024'],
                      indian_data['ranking_distribution']['2024']['top_100'],
                      indian_data['ranking_distribution']['2024']['top_200'],
                      indian_data['ranking_distribution']['2024']['top_500']]
        values_2025 = [indian_data['participation']['2025'],
                      indian_data['ranking_distribution']['2025']['top_100'],
                      indian_data['ranking_distribution']['2025']['top_200'],
                      indian_data['ranking_distribution']['2025']['top_500']]

        x = np.arange(len(categories))
        width = 0.35

        bars1 = ax6.bar(x - width/2, values_2024, width, label='2024',
                       color=self.COLOR_PALETTE['primary'], alpha=0.8)
        bars2 = ax6.bar(x + width/2, values_2025, width, label='2025',
                       color=self.COLOR_PALETTE['india'], alpha=0.8)

        ax6.set_xlabel('Categories', fontsize=14)
        ax6.set_ylabel('Number of Institutions', fontsize=14)
        ax6.set_title('Indian Institutions Growth Analysis', fontsize=16, fontweight='bold')
        ax6.set_xticks(x)
        ax6.set_xticklabels(categories)
        ax6.legend()

        # Add values on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax6.text(bar.get_x() + bar.get_width()/2, height + 0.5,
                        f'{int(height)}', ha='center', va='bottom', fontsize=11, fontweight='bold')

        # 4. Global Competitive Position (Bottom)
        ax7 = fig.add_subplot(gs[2, :])

        # Top 10 Indian institutions comparison with global benchmarks
        top_indian_2025 = self.data['india_2025'].nsmallest(10, 'rank_current_midpoint')

        # Create horizontal bar chart
        y_pos = np.arange(len(top_indian_2025))
        institutions = [name[:35] + '...' if len(name) > 35 else name for name in top_indian_2025['institution_name']]
        ranks = top_indian_2025['rank_current_midpoint']
        scores = top_indian_2025['score_midpoint']

        # Color code: Symbiosis in special color, others in standard color
        colors = [self.COLOR_PALETTE['symbiosis'] if 'symbiosis' in name.lower()
                 else self.COLOR_PALETTE['india'] for name in institutions]

        bars = ax7.barh(y_pos, ranks, color=colors, alpha=0.8)
        ax7.set_yticks(y_pos)
        ax7.set_yticklabels(institutions, fontsize=11)
        ax7.set_xlabel('Global Ranking Position', fontsize=14)
        ax7.set_title('Top 10 Indian Institutions - Global Rankings 2025', fontsize=16, fontweight='bold')
        ax7.invert_xaxis()  # Lower rank number is better
        ax7.invert_yaxis()  # Top institution at top

        # Add rank values and scores on bars
        for i, (bar, rank, score) in enumerate(zip(bars, ranks, scores)):
            # Rank on the bar
            ax7.text(rank - 5, bar.get_y() + bar.get_height()/2,
                    f'{int(rank)}', ha='right', va='center', fontweight='bold', color='white', fontsize=10)
            # Score at the end
            ax7.text(ax7.get_xlim()[1] * 0.95, bar.get_y() + bar.get_height()/2,
                    f'Score: {score:.1f}', ha='right', va='center', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))

        # Add global benchmark lines
        ax7.axvline(x=10, color='red', linestyle='--', alpha=0.7, linewidth=2)
        ax7.axvline(x=25, color='orange', linestyle='--', alpha=0.7, linewidth=2)
        ax7.axvline(x=100, color='green', linestyle='--', alpha=0.7, linewidth=2)

        # Add benchmark labels
        ax7.text(10, len(top_indian_2025) - 0.5, 'Top 10', rotation=90, ha='right', va='top',
                color='red', fontweight='bold', fontsize=10)
        ax7.text(25, len(top_indian_2025) - 0.5, 'Top 25', rotation=90, ha='right', va='top',
                color='orange', fontweight='bold', fontsize=10)
        ax7.text(100, len(top_indian_2025) - 0.5, 'Top 100', rotation=90, ha='right', va='top',
                color='green', fontweight='bold', fontsize=10)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Executive dashboard saved to {save_path}")
        return save_path

    def _create_kpi_card(self, ax, title: str, value: str, subtitle: str, color: str):
        """Create a KPI card visualization."""
        ax.text(0.5, 0.7, value, ha='center', va='center', fontsize=28, fontweight='bold',
                color=color, transform=ax.transAxes)
        ax.text(0.5, 0.4, title, ha='center', va='center', fontsize=12, fontweight='bold',
                transform=ax.transAxes)
        ax.text(0.5, 0.2, subtitle, ha='center', va='center', fontsize=10,
                color='gray', transform=ax.transAxes)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

        # Add border
        for spine in ax.spines.values():
            spine.set_visible(True)
            spine.set_linewidth(2)
            spine.set_edgecolor(color)
            spine.set_alpha(0.3)

    def create_interactive_dashboard(self, save_path: str = "output/figures/interactive_dashboard.html") -> str:
        """
        Create interactive dashboard using Plotly.

        Parameters
        ----------
        save_path : str
            Path to save the interactive dashboard

        Returns
        -------
        str
            Path to saved dashboard
        """
        # Get data
        indian_data = self.indian_analyzer.analyze_overall_performance()
        growth_data = self.indian_analyzer.analyze_growth_trends()
        global_data = self.global_analyzer.analyze_global_leaders()

        # Create subplots
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=('Indian Institutions Growth', 'Performance Distribution',
                           'Top Performers Trend', 'Global Comparison',
                           'Score vs Rank Analysis', 'Regional Competition'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": True}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]],
            vertical_spacing=0.12,
            horizontal_spacing=0.1
        )

        # 1. Indian Institutions Growth
        categories = ['Total', 'Top 100', 'Top 200', 'Top 500']
        values_2024 = [indian_data['participation']['2024'],
                      indian_data['ranking_distribution']['2024']['top_100'],
                      indian_data['ranking_distribution']['2024']['top_200'],
                      indian_data['ranking_distribution']['2024']['top_500']]
        values_2025 = [indian_data['participation']['2025'],
                      indian_data['ranking_distribution']['2025']['top_100'],
                      indian_data['ranking_distribution']['2025']['top_200'],
                      indian_data['ranking_distribution']['2025']['top_500']]

        fig.add_trace(go.Bar(name='2024', x=categories, y=values_2024,
                            marker_color=self.COLOR_PALETTE['primary']), row=1, col=1)
        fig.add_trace(go.Bar(name='2025', x=categories, y=values_2025,
                            marker_color=self.COLOR_PALETTE['india']), row=1, col=1)

        # 2. Performance Distribution
        fig.add_trace(go.Histogram(x=self.data['india_2024']['score_midpoint'],
                                  name='2024 Scores', opacity=0.7,
                                  marker_color=self.COLOR_PALETTE['primary']), row=1, col=2)
        fig.add_trace(go.Histogram(x=self.data['india_2025']['score_midpoint'],
                                  name='2025 Scores', opacity=0.7,
                                  marker_color=self.COLOR_PALETTE['india']), row=1, col=2)

        # 3. Top Performers Trend
        if len(growth_data['trend_data']) > 0:
            trend_df = pd.DataFrame(growth_data['trend_data'])
            top_10_trends = trend_df.nlargest(10, 'rank_change')

            fig.add_trace(go.Scatter(x=top_10_trends['score_2024'], y=top_10_trends['rank_2024'],
                                    mode='markers', name='2024 Position',
                                    marker=dict(size=10, color=self.COLOR_PALETTE['primary']),
                                    text=top_10_trends['institution'],
                                    hovertemplate='<b>%{text}</b><br>Rank: %{y}<br>Score: %{x}<extra></extra>'),
                         row=2, col=1)
            fig.add_trace(go.Scatter(x=top_10_trends['score_2025'], y=top_10_trends['rank_2025'],
                                    mode='markers', name='2025 Position',
                                    marker=dict(size=10, color=self.COLOR_PALETTE['success']),
                                    text=top_10_trends['institution'],
                                    hovertemplate='<b>%{text}</b><br>Rank: %{y}<br>Score: %{x}<extra></extra>'),
                         row=2, col=1)

        # 4. Global Comparison - Top Countries
        top_countries = list(global_data['country_representation']['2025'].keys())[:10]
        country_counts = [global_data['country_representation']['2025'][country] for country in top_countries]

        colors = [self.COLOR_PALETTE['india'] if country == 'India' else self.COLOR_PALETTE['primary']
                 for country in top_countries]

        fig.add_trace(go.Bar(x=country_counts, y=top_countries, orientation='h',
                            marker_color=colors, name='Institutions in Top 50'), row=2, col=2)

        # 5. Score vs Rank Analysis
        fig.add_trace(go.Scatter(x=self.data['india_2025']['score_midpoint'],
                                y=self.data['india_2025']['rank_current_midpoint'],
                                mode='markers', name='Indian Institutions 2025',
                                marker=dict(size=8, color=self.COLOR_PALETTE['india'], opacity=0.7),
                                text=self.data['india_2025']['institution_name'],
                                hovertemplate='<b>%{text}</b><br>Rank: %{y}<br>Score: %{x}<extra></extra>'),
                       row=3, col=1)

        # 6. Regional Competition
        asian_countries = ['India', 'China', 'Japan', 'South Korea', 'Singapore', 'Malaysia', 'Thailand']
        asian_best_ranks = []
        asian_counts = []

        for country in asian_countries:
            country_data = self.data['df_2025'][self.data['df_2025']['country_name'] == country]
            if len(country_data) > 0:
                asian_best_ranks.append(country_data['rank_current_midpoint'].min())
                asian_counts.append(len(country_data))
            else:
                asian_best_ranks.append(None)
                asian_counts.append(0)

        # Filter out None values
        valid_data = [(country, rank, count) for country, rank, count in zip(asian_countries, asian_best_ranks, asian_counts) if rank is not None]
        if valid_data:
            countries, ranks, counts = zip(*valid_data)
            colors = [self.COLOR_PALETTE['india'] if country == 'India' else self.COLOR_PALETTE['primary']
                     for country in countries]

            fig.add_trace(go.Scatter(x=counts, y=ranks, mode='markers+text',
                                    marker=dict(size=15, color=colors),
                                    text=countries, textposition='top center',
                                    name='Asian Countries',
                                    hovertemplate='<b>%{text}</b><br>Best Rank: %{y}<br>Total Institutions: %{x}<extra></extra>'),
                         row=3, col=2)

        # Update layout
        fig.update_layout(
            title=dict(
                text="THE Impact Rankings 2024-2025: Interactive Analysis Dashboard",
                font=dict(size=24, family="Arial, sans-serif"),
                x=0.5
            ),
            showlegend=True,
            height=1200,
            font=dict(family="Arial, sans-serif", size=12),
            plot_bgcolor='white',
            paper_bgcolor='white'
        )

        # Update axes labels
        fig.update_xaxes(title_text="Categories", row=1, col=1)
        fig.update_yaxes(title_text="Number of Institutions", row=1, col=1)

        fig.update_xaxes(title_text="Score", row=1, col=2)
        fig.update_yaxes(title_text="Frequency", row=1, col=2)

        fig.update_xaxes(title_text="Score", row=2, col=1)
        fig.update_yaxes(title_text="Rank", row=2, col=1)

        fig.update_xaxes(title_text="Institutions in Top 50", row=2, col=2)
        fig.update_yaxes(title_text="Countries", row=2, col=2)

        fig.update_xaxes(title_text="Score", row=3, col=1)
        fig.update_yaxes(title_text="Rank", row=3, col=1)

        fig.update_xaxes(title_text="Total Institutions", row=3, col=2)
        fig.update_yaxes(title_text="Best Rank", row=3, col=2)

        # Invert y-axis for ranking plots (lower rank is better)
        fig.update_yaxes(autorange="reversed", row=2, col=1)
        fig.update_yaxes(autorange="reversed", row=3, col=1)
        fig.update_yaxes(autorange="reversed", row=3, col=2)

        # Save interactive dashboard
        pyo.plot(fig, filename=save_path, auto_open=False)

        logger.info(f"Interactive dashboard saved to {save_path}")
        return save_path

    def create_comprehensive_report_visualizations(self) -> Dict[str, str]:
        """
        Create all visualizations for the comprehensive report.

        Returns
        -------
        Dict[str, str]
            Dictionary mapping visualization names to file paths
        """
        visualizations = {}

        # Create all individual visualizations
        logger.info("Creating executive dashboard...")
        visualizations['executive_dashboard'] = self.create_executive_dashboard()

        logger.info("Creating interactive dashboard...")
        visualizations['interactive_dashboard'] = self.create_interactive_dashboard()

        logger.info("Creating Symbiosis analysis visualization...")
        visualizations['symbiosis_analysis'] = self.symbiosis_analyzer.create_performance_visualization()

        logger.info("Creating Indian institutions analysis...")
        visualizations['indian_institutions'] = self.indian_analyzer.create_comprehensive_visualization()

        logger.info("Creating global benchmarking analysis...")
        visualizations['global_benchmarking'] = self.global_analyzer.create_global_benchmarking_visualization()

        return visualizations

    def generate_visualization_summary(self, visualizations: Dict[str, str]) -> str:
        """
        Generate a summary of all created visualizations.

        Parameters
        ----------
        visualizations : Dict[str, str]
            Dictionary of visualization names and paths

        Returns
        -------
        str
            Summary text
        """
        summary = []
        summary.append("="*80)
        summary.append("COMPREHENSIVE VISUALIZATION SUITE - THE IMPACT RANKINGS ANALYSIS")
        summary.append("="*80)
        summary.append("")
        summary.append("📊 VISUALIZATION SUITE CREATED SUCCESSFULLY")
        summary.append("")

        viz_descriptions = {
            'executive_dashboard': "Executive Dashboard - Key metrics and performance indicators",
            'interactive_dashboard': "Interactive Dashboard - Dynamic analysis with hover details",
            'symbiosis_analysis': "Symbiosis Analysis - Detailed performance trends and positioning",
            'indian_institutions': "Indian Institutions Analysis - Comprehensive growth and trends",
            'global_benchmarking': "Global Benchmarking - International competitive analysis"
        }

        for viz_name, viz_path in visualizations.items():
            description = viz_descriptions.get(viz_name, "Analysis visualization")
            summary.append(f"   📈 {description}")
            summary.append(f"      File: {viz_path}")
            summary.append("")

        summary.append("🎯 KEY FEATURES:")
        summary.append("   • Professional styling with consistent branding")
        summary.append("   • Interactive elements for detailed exploration")
        summary.append("   • High-resolution outputs suitable for presentations")
        summary.append("   • Comprehensive coverage of all analysis dimensions")
        summary.append("   • Strategic insights highlighted throughout")
        summary.append("")

        summary.append("📋 USAGE RECOMMENDATIONS:")
        summary.append("   • Use Executive Dashboard for leadership presentations")
        summary.append("   • Share Interactive Dashboard for detailed exploration")
        summary.append("   • Include specific analysis charts in detailed reports")
        summary.append("   • Combine visualizations for comprehensive documentation")
        summary.append("")

        summary.append("="*80)

        return "\n".join(summary)

def run_complete_visualization_suite():
    """Main function to create complete visualization suite."""
    suite = VisualizationSuite()

    print("Creating comprehensive visualization suite...")
    print("This may take a few minutes to generate all visualizations...")

    # Create all visualizations
    visualizations = suite.create_comprehensive_report_visualizations()

    # Generate and print summary
    summary = suite.generate_visualization_summary(visualizations)
    print(summary)

    return visualizations

if __name__ == "__main__":
    run_complete_visualization_suite()